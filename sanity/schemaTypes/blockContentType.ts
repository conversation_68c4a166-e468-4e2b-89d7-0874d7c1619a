import { defineType, defineArrayMember } from 'sanity'
import {
  CaptionDecorator,
  CaptionIcon,
  HighlightDecorator,
  HighlightIcon,
} from '@/components/sanity/RichTextEditor'
import { UserIcon } from '@sanity/icons'
import ExternalLinkRenderer from '@/components/sanity/ExternalLinkRenderer'

export const blockContentType = defineType({
  title: 'Block Content',
  name: 'blockContent',
  type: 'array',
  of: [
    defineArrayMember({
      type: 'block',
      styles: [
        { title: 'Normal', value: 'normal' },
        { title: 'H1', value: 'h1' },
        { title: 'H2', value: 'h2' },
        { title: 'H3', value: 'h3' },
        { title: 'H4', value: 'h4' },
        { title: 'Quote', value: 'blockquote' },
      ],
      lists: [
        { title: 'Bullet', value: 'bullet' },
        { title: 'Numbered', value: 'number' },
      ],
      marks: {
        decorators: [
          { title: 'Strong', value: 'strong' },
          { title: 'Emphasis', value: 'em' },
          { title: 'Underline', value: 'underline' },
          { title: 'Strike', value: 'strike-through' },
          {
            title: 'Caption',
            value: 'caption',
            icon: CaptionIcon,
            component: CaptionDecorator,
          },
          {
            title: 'Highlight',
            value: 'highlight',
            icon: HighlightIcon,
            component: HighlightDecorator,
          },
        ],
        annotations: [
          {
            name: 'link',
            type: 'object',
            title: 'link',
            fields: [
              {
                name: 'url',
                type: 'url',
              },
            ],
            validation: (Rule) =>
              Rule.regex(
                /https:\/\/(www\.|)(portabletext\.org|sanity\.io)\/.*/gi,
                {
                  name: 'internal url',
                  invert: true,
                }
              ).warning(
                `This is not an external link. Consider using internal links instead.`
              ),
            components: {
              annotation: ExternalLinkRenderer,
            },
          },
          {
            name: 'internalLink',
            type: 'object',
            title: 'Internal link',
            icon: UserIcon,
            fields: [
              {
                name: 'reference',
                type: 'reference',
                title: 'Reference',
                to: [{ type: 'post' }],
              },
            ],
          },
        ],
      },
    }),
    defineArrayMember({
      type: 'image',
      options: { hotspot: true },
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative Text',
        },
      ],
    }),
    defineArrayMember({
      type: 'code',
    }),
  ],
})
