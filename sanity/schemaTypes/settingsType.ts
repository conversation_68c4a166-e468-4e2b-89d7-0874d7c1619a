import { CogIcon } from "@sanity/icons";
import {  defineField, defineType } from "sanity";

export const settingsType = defineType({
  name: 'settings',
  type: 'document',
  title: 'Settings',
  icon: CogIcon,
  fieldsets: [
    {
      title: 'SEO & Metadata',
      name: 'metadata',
      options: {
        collapsible: true,
        collapsed: false,
      },
    },
    {
      title: 'Social Media',
      name: 'social',
    },
    {
      title: 'Website Logo',
      name: 'logos',
      options: {
        collapsible: true,
        collapsed: false,
      },
    },
    {
      title: 'Maintenance Mode',
      name: 'maintenance',
      options: {
        collapsible: true,
        collapsed: true,
      },
    },
  ],
  fields: [
    // General Information
    defineField({
      name: 'siteTitle',
      type: 'string',
      title: 'Site Title',
      description: 'Enter the primary title of the website.',
    }),
    defineField({
      name: 'siteDescription',
      type: 'text',
      title: 'Site Description',
      description: 'Enter a brief description of the website.',
    }),
    defineField({
      title: 'URL',
      name: 'url',
      type: 'url',
      description: 'The main site URL. Used to create canonical URLs.',
    }),
    defineField({
      name: 'copyright',
      type: 'string',
      title: 'Copyright Name',
      description: 'Enter the company name to appear in the footer after ©.',
    }),

    // Logos
    defineField({
      title: 'Main Logo',
      description: 'Upload your main logo here. SVG preferred.',
      name: 'logo',
      type: 'image',
      fieldset: 'logos',
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative Text',
          description: 'Important for SEO and accessibility.',
        },
      ],
    }),
    defineField({
      title: 'Alternate Logo (optional)',
      description:
        'Upload alternate logo here. It can be light/dark variation.',
      name: 'logoAlt',
      type: 'image',
      fieldset: 'logos',
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative Text',
          description: 'Important for SEO and accessibility.',
        },
      ],
    }),
    defineField({
      name: 'favicon',
      type: 'image',
      title: 'Favicon',
      description: 'Upload the favicon (32x32px PNG or ICO).',
      fieldset: 'logos',
    }),

    // Contact Information
    defineField({
      name: 'email',
      type: 'string',
      title: 'Support Email',
      validation: (Rule) =>
        Rule.regex(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/, {
          name: 'email', // Error message is "Does not match email-pattern"
          invert: false,
        }),
    }),
    defineField({
      name: 'phone',
      type: 'string',
      title: 'Support Phone',
    }),

    // Social Media
    defineField({
      name: 'social',
      type: 'array',
      title: 'Social Links',
      description: 'Enter your social media URLs.',
      validation: (Rule) => Rule.unique(),
      of: [
        {
          type: 'object',
          fields: [
            {
              type: 'string',
              name: 'media',
              title: 'Choose Social Media',
              options: {
                list: [
                  { title: 'Twitter', value: 'twitter' },
                  { title: 'Facebook', value: 'facebook' },
                  { title: 'Instagram', value: 'instagram' },
                  { title: 'LinkedIn', value: 'linkedin' },
                  { title: 'YouTube', value: 'youtube' },
                ],
              },
            },
            {
              type: 'url',
              name: 'url',
              title: 'Full Profile URL',
            },
          ],
          preview: {
            select: {
              title: 'media',
              subtitle: 'url',
            },
          },
        },
      ],
    }),
    defineField({
      name: 'twitterHandle',
      type: 'string',
      title: 'Twitter Handle',
      description: 'Enter your Twitter handle for meta tags.',
    }),
    defineField({
      name: 'facebookAppId',
      type: 'string',
      title: 'Facebook App ID',
      description: 'Enter your Facebook App ID for sharing and analytics.',
    }),

    // SEO & Metadata
    defineField({
      title: 'Meta Description',
      name: 'description',
      fieldset: 'metadata',
      type: 'text',
      rows: 5,
      validation: (Rule) => Rule.min(20).max(200),
      description: 'Enter SEO meta description.',
    }),
    defineField({
      name: 'openGraphImage',
      type: 'image',
      title: 'Open Graph Image',
      description: 'Image for sharing previews on Facebook, Twitter, etc.',
      fieldset: 'metadata',
    }),
    defineField({
      name: 'canonicalUrl',
      type: 'url',
      title: 'Canonical URL',
      description: 'Specify the canonical URL for the homepage.',
    }),
    defineField({
      name: 'keywords',
      type: 'array',
      title: 'SEO Keywords',
      description: 'Add a list of keywords for better search indexing.',
      of: [{ type: 'string' }],
      validation: (Rule) => Rule.unique(),
    }),

    // Analytics
    defineField({
      name: 'analyticsId',
      type: 'string',
      title: 'Analytics Tracking ID',
      description:
        'Enter your Google Analytics or similar service tracking ID.',
    }),

    // Cookie Consent
    defineField({
      name: 'cookieConsentText',
      type: 'text',
      title: 'Cookie Consent Message',
      description: 'Text to display for cookie consent pop-up.',
    }),

    // Maintenance Mode
    defineField({
      name: 'maintenanceMode',
      type: 'boolean',
      title: 'Enable Maintenance Mode',
      description: 'Temporarily disable the website for visitors.',
      fieldset: 'maintenance',
    }),
    defineField({
      name: 'maintenanceMessage',
      type: 'text',
      title: 'Maintenance Message',
      description: 'Custom message to display during maintenance mode.',
      rows: 3,
      fieldset: 'maintenance',
    }),

    // Localization
    defineField({
      name: 'language',
      type: 'string',
      title: 'Default Language',
      description: 'Specify the default language (e.g., en, es, fr).',
      validation: (Rule) => Rule.required(),
      options: {
        list: [
          { title: 'English', value: 'en' },
          { title: 'Spanish', value: 'es' },
          { title: 'French', value: 'fr' },
          { title: 'German', value: 'de' },
        ],
      },
    }),
    defineField({
      name: 'locales',
      type: 'array',
      title: 'Supported Locales',
      description: 'Specify supported locales for multi-language support.',
      of: [{ type: 'string' }],
    }),
    defineField({
      name: 'enableSubscribeForm',
      type: 'boolean',
      title: 'Enable Subscribe Form',
      initialValue: false,
    }),
  ],
})

export default settingsType;
