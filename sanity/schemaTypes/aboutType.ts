import { defineField, defineType } from 'sanity'
import { InfoFilledIcon } from '@sanity/icons'
export const aboutType = defineType({
  name: 'aboutPage',
  title: 'About Page',
  type: 'document',
  icon: InfoFilledIcon,
  fields: [
    defineField({
      name: 'title',
      title: 'Page Title',
      type: 'string',
      description: 'The title of the About page for internal use.',
    }),
    defineField({
      name: 'hero',
      title: 'Hero Section',
      type: 'object',
      fields: [
        defineField({
          name: 'image',
          title: 'Profile Image',
          type: 'image',
          options: { hotspot: true },
        }),
        defineField({
          name: 'name',
          title: 'Name',
          type: 'string',
        }),
        defineField({
          name: 'introduction',
          title: 'Introduction',
          type: 'text',
        }),
        defineField({
          name: 'niche',
          title: 'Niche',
          type: 'string',
        }),
      ],
    }),
    defineField({
      name: 'mission',
      title: 'Mission Section',
      type: 'object',
      fields: [
        {
          name: 'heading',
          title: 'Heading',
          type: 'string',
        },
        {
          name: 'description',
          title: 'Description',
          type: 'text',
        },
        {
          name: 'focusArea',
          title: 'Focus Area',
          type: 'string',
        },
      ],
    }),
    defineField({
      name: 'socialProof',
      title: 'Social Proof',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
            },
            {
              name: 'value',
              title: 'Value',
              type: 'number',
            },
          ],
        },
      ],
    }),
    defineField({
      name: 'callToAction',
      title: 'Call to Action',
      type: 'object',
      fields: [
        {
          name: 'heading',
          title: 'Heading',
          type: 'string',
        },
        {
          name: 'description',
          title: 'Description',
          type: 'text',
        },
        {
          name: 'buttonText',
          title: 'Button Text',
          type: 'string',
        },
        {
          name: 'link',
          title: 'Button Link',
          type: 'url',
        },
      ],
    }),
    defineField({
      name: 'contact',
      title: 'Contact Section',
      type: 'object',
      fields: [
        {
          name: 'heading',
          title: 'Heading',
          type: 'string',
        },
        {
          name: 'description',
          title: 'Description',
          type: 'text',
        },
        {
          name: 'contactLink',
          title: 'Contact Link',
          type: 'url',
        },
      ],
    }),
    defineField({
      name: 'enableSubscribeForm',
      type: 'boolean',
      title: 'Enable Subscribe Form',
      initialValue: false,
    }),
    defineField({
      name: 'enableSocialProof',
      type: 'boolean',
      title: 'Enable Social Proof',
      initialValue: false,
    }),
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'hero.name',
      media: 'hero.profileImage',
    },
    prepare({ title, subtitle, media }) {
      return {
        title: title || 'Untitled About Page',
        subtitle: subtitle ? `Hero: ${subtitle}` : 'No Hero Name',
        media: media,
      }
    },
  },
})
