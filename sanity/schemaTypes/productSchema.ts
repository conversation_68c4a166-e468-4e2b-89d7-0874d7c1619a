import { defineField, defineType } from 'sanity'

export const productType = defineType({
  name: 'product',
  title: 'Product',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Product Name',
      type: 'string',
      validation: (Rule) =>
        Rule.required()
          .min(5)
          .warning('Product name should have at least 5 characters.'),
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
        slugify: (input) => {
          if (typeof input !== 'string') {
            return ''
          }
          return input
            .toLowerCase()
            .replace(/[^\w\s-]/g, '-') // Replace special chars with hyphen
            .replace(/\s+/g, '-') // Replace spaces with hyphen
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
            .slice(0, 96) // Ensure max length
        },
      },
      description:
        "Used to generate the URL for this product. Click 'Generate' to create a slug from the name.",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 3,
      validation: (Rule) => Rule.max(2000),
    }),
    defineField({
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative text',
          description: 'Important for SEO and accessibility.',
        },
      ],
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'productImages',
      title: 'Additional Product Images',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true,
          },
        },
      ],
      validation: (Rule) => Rule.max(10),
    }),
    defineField({
      name: 'price',
      title: 'Price',
      type: 'number',
      validation: (Rule) => Rule.required().min(0).precision(2),
    }),
    defineField({
      name: 'currencySymbol',
      title: 'Currency Symbol',
      type: 'string',
      options: {
        list: [
          { title: 'USD ($)', value: 'USD' },
          { title: 'EUR (€)', value: 'EUR' },
          { title: 'GBP (£)', value: 'GBP' },
          { title: 'INR (₹)', value: 'INR' },
          { title: 'CAD ($)', value: 'CAD' },
        ],
        layout: 'dropdown',
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'productCategory',
      title: 'Product Category',
      type: 'reference',
      to: [{ type: 'productCategory' }],
      validation: (Rule) => Rule.required(),
      options: {
        filter: ({ document: _document }) => {
          // Only show active categories
          return {
            filter: 'isActive == true',
            params: {},
          }
        },
      },
    }),
    defineField({
      name: 'supportedCountries',
      title: 'Supported Countries',
      type: 'array',
      of: [
        {
          type: 'string',
          options: {
            list: [
              { title: 'United States', value: 'United States' },
              { title: 'Canada', value: 'Canada' },
              { title: 'United Kingdom', value: 'United Kingdom' },
              { title: 'India', value: 'India' },
              { title: 'Australia', value: 'Australia' },
            ],
          },
        },
      ],
      validation: (Rule) => Rule.required().min(1),
    }),
    defineField({
      name: 'specifications',
      title: 'Product Specifications',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'key',
              title: 'Specification Name',
              type: 'string',
            },
            {
              name: 'value',
              title: 'Specification Value',
              type: 'string',
            },
          ],
        },
      ],
    }),
    defineField({
      name: 'affiliateLink',
      title: 'Affiliate Link',
      type: 'url',
      validation: (Rule) => Rule.uri({ scheme: ['https', 'http'] }),
    }),
    defineField({
      name: 'retailer',
      title: 'Retailer',
      type: 'string',
      options: {
        list: [
          { title: 'Amazon', value: 'Amazon' },
          { title: 'Flipkart', value: 'Flipkart' },
          { title: 'Walmart', value: 'Walmart' },
          { title: 'iHerb', value: 'iHerb' },
          { title: 'Other', value: 'Other' },
        ],
        layout: 'dropdown',
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'popularity',
      title: 'Popularity Score',
      type: 'number',
      description: 'Higher numbers appear first when sorted by popularity',
      validation: (Rule) => Rule.min(0).max(100),
    }),
    defineField({
      name: 'creationDate',
      title: 'Creation Date',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    }),
  ],
  preview: {
    select: {
      title: 'name',
      retailer: 'retailer',
      media: 'mainImage',
      price: 'price',
      currency: 'currencySymbol',
    },
    prepare(selection) {
      const { retailer, price, currency } = selection
      return {
        ...selection,
        subtitle: [
          retailer ? `from ${retailer}` : '',
          price && currency ? `${currency} ${price.toFixed(2)}` : '',
        ]
          .filter(Boolean)
          .join(' | ')
          .replace(/\s+\|/, ' |')
          .replace(/\| $/, ''),
      }
    },
  },
})
