import { defineType,defineField } from "sanity";
import { fieldSets } from "@/sanity/utils/fieldSets";
import { fieldGroups } from "@/sanity/utils/fieldGroups";
import { seoFields } from "@/sanity/utils/seoFields";


export default defineType({
  name: "privacyPage",
  title: "Privacy Page",
  type: "document",
  fieldsets: [...fieldSets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: "heading",
      title: "Heading",
      type: "string",
      group: "content",
      fieldset: "content",
    }),
    defineField({
      name: "content",
      title: "Content",
      type: "array",
      of: [{ type: "block" }],
    }),
    ...seoFields,
  ],
});
