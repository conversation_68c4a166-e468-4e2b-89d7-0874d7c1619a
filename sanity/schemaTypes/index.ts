import { type SchemaTypeDefinition } from 'sanity'

import { blockContentType } from '@/sanity/schemaTypes/blockContentType'
import { categoryType } from '@/sanity/schemaTypes/categoryType'
import { postType } from '@/sanity/schemaTypes/postType'
import { authorType } from '@/sanity/schemaTypes/authorType'
import { settingsType } from '@/sanity/schemaTypes/settingsType'
import privacyPageType from '@/sanity/schemaTypes/privacyPageType'
import termsPageType from '@/sanity/schemaTypes/termsPageType'
import { aboutType } from '@/sanity/schemaTypes/aboutType'
import { productType } from '@/sanity/schemaTypes/productSchema'
import { productCategoryType } from '@/sanity/schemaTypes/productCategoryType'

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    blockContentType,
    categoryType,
    postType,
    authorType,
    settingsType,
    privacyPageType,
    termsPageType,
    aboutType,
    productType,
    productCategoryType,
  ],
}
