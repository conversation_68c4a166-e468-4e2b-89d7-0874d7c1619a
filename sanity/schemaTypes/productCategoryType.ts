import { TagIcon } from '@sanity/icons'
import { defineField, defineType } from 'sanity'

export const productCategoryType = defineType({
  name: 'productCategory',
  title: 'Product Category',
  type: 'document',
  icon: TagIcon,
  fields: [
    defineField({
      name: 'name',
      title: 'Category Name',
      type: 'string',
      validation: (Rule) =>
        Rule.required()
          .min(2)
          .max(50)
          .warning('Category name should be between 2-50 characters'),
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
        slugify: (input) =>
          input
            .toLowerCase()
            .replace(/[^\w\s-]/g, '-') // Replace special chars with hyphen
            .replace(/\s+/g, '-') // Replace spaces with hyphen
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
            .slice(0, 96), // Ensure max length
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 3,
      validation: (Rule) => Rule.max(200),
    }),
    defineField({
      name: 'color',
      title: 'Category Color',
      type: 'string',
      description: 'Color theme for this category in the UI',
      options: {
        list: [
          { title: 'Blue', value: 'blue' },
          { title: 'Green', value: 'green' },
          { title: 'Purple', value: 'purple' },
          { title: 'Orange', value: 'orange' },
          { title: 'Red', value: 'red' },
          { title: 'Indigo', value: 'indigo' },
          { title: 'Pink', value: 'pink' },
          { title: 'Teal', value: 'teal' },
        ],
        layout: 'dropdown',
      },
      initialValue: 'blue',
    }),
    defineField({
      name: 'icon',
      title: 'Category Icon',
      type: 'string',
      description: 'Icon name for this category (optional)',
      options: {
        list: [
          { title: 'Electronics', value: 'electronics' },
          { title: 'Home & Garden', value: 'home' },
          { title: 'Supplements', value: 'supplements' },
          { title: 'Vitamins', value: 'vitamins' },
          { title: 'Protein', value: 'protein' },
          { title: 'Fitness', value: 'fitness' },
          { title: 'Health Food', value: 'food' },
          { title: 'Wellness', value: 'wellness' },
          { title: 'Beauty', value: 'beauty' },
          { title: 'Sports', value: 'sports' },
          { title: 'Workplace', value: 'workplace' },
        ],
        layout: 'dropdown',
      },
    }),
    defineField({
      name: 'isActive',
      title: 'Active',
      type: 'boolean',
      description: 'Whether this category is active and should be shown',
      initialValue: true,
    }),
    defineField({
      name: 'sortOrder',
      title: 'Sort Order',
      type: 'number',
      description: 'Order in which categories should appear (lower numbers first)',
      validation: (Rule) => Rule.min(0).max(100),
      initialValue: 10,
    }),
    defineField({
      name: 'seoTitle',
      title: 'SEO Title',
      type: 'string',
      description: 'Custom title for SEO purposes',
      validation: (Rule) => Rule.max(60),
    }),
    defineField({
      name: 'seoDescription',
      title: 'SEO Description',
      type: 'text',
      description: 'Custom description for SEO purposes',
      validation: (Rule) => Rule.max(160),
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'description',
      isActive: 'isActive',
      sortOrder: 'sortOrder',
    },
    prepare(selection) {
      const { title, subtitle, isActive, sortOrder } = selection
      return {
        title: `${title}${!isActive ? ' (Inactive)' : ''}`,
        subtitle: subtitle || `Sort order: ${sortOrder || 'Not set'}`,
      }
    },
  },
  orderings: [
    {
      title: 'Sort Order',
      name: 'sortOrderAsc',
      by: [{ field: 'sortOrder', direction: 'asc' }],
    },
    {
      title: 'Name A-Z',
      name: 'nameAsc',
      by: [{ field: 'name', direction: 'asc' }],
    },
    {
      title: 'Created (newest first)',
      name: 'createdDesc',
      by: [{ field: '_createdAt', direction: 'desc' }],
    },
  ],
})
