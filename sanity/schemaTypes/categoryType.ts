import { TagIcon } from '@sanity/icons'
import { defineField, defineType } from 'sanity'

export const categoryType = defineType({
  name: 'category',
  title: 'Category',
  type: 'document',
  icon:  TagIcon,
  fields: [
    defineField({
      name: 'title',
      type: 'string',
      validation: (Rule) =>
        Rule.required()
          .min(3)
          .warning('Title should have at least 3 characters'),
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
        slugify: (input) =>
          input.toLowerCase().replace(/\s+/g, '-').slice(0, 200),
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'description',
      type: 'text',
      validation: (Rule) =>
        Rule.max(200).warning(
          'Descriptions should be concise and under 200 characters'
        ),
    }),
    defineField({
      name: 'color',
      title: 'Color',
      type: 'string',
      description: 'Color of the category',
      options: {
        list: [
          { title: 'Green', value: 'green' },
          { title: 'Blue', value: 'blue' },
          { title: 'Purple', value: 'purple' },
          { title: 'Orange', value: 'orange' },
          { title: 'Yellow', value: 'yellow' },
        ],
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'customColor',
      title: 'Custom Color',
      type: 'string',
      description: 'Enter a hex code for a custom color (e.g., #ff5733)',
      validation: (Rule) =>
        Rule.regex(/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/, {
          name: 'hex code',
        }).error('Must be a valid hex color code'),
    }),
  ],
})
