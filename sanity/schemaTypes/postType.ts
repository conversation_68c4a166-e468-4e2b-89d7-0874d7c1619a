import { DocumentTextIcon } from '@sanity/icons'
import { defineField, defineType } from 'sanity'

export const postType = defineType({
  name: 'post',
  title: 'Post',
  type: 'document',
  icon: DocumentTextIcon,
  initialValue: () => ({
    publishedAt: new Date().toISOString(),
    featured: false,
  }),
  fields: [
    defineField({
      name: 'title',
      type: 'string',
      validation: (Rule) =>
        Rule.required()
          .min(5)
          .warning('Title should have at least 5 characters.'),
    }),
    defineField({
      name: 'slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
        slugify: (input) => {
          if (typeof input !== 'string') {
            return ''
          }
          return input
            .toLowerCase()
            .replace(/[^\w\s-]/g, '-') // Replace special chars with hyphen
            .replace(/\s+/g, '-') // Replace spaces with hyphen
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
            .slice(0, 96) // Ensure max length
        },
      },
      description:
        "Used to generate the URL for this post. Click 'Generate' to create a slug from the title.",
      validation: (Rule) => Rule.required(),
    }),
    {
      name: 'excerpt',
      title: 'Excerpt',
      description: 'A short description of the post (150-160 words recommended',
      type: 'text',
      rows: 3,
      validation: (Rule) => Rule.required().max(2000),
    },
    defineField({
      name: 'author',
      title: 'Author',
      type: 'reference',
      to: { type: 'author' },
    }),
    {
      name: 'mainImage',
      title: 'Main image',
      type: 'image',
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: 'Alternative text',
          description: 'Important for SEO and accessibility.',
          validation: (Rule) => Rule.required(),
        },
      ],
      options: {
        hotspot: true,
      },
    },
    defineField({
      name: 'categories',
      title: 'Categories',
      type: 'array',
      of: [{ type: 'reference', to: { type: 'category' } }],
      description: 'Select at least one category for this post.',
      validation: (Rule) => Rule.min(1).error('Select at least one category.'),
    }),
    defineField({
      name: 'publishedAt',
      title: 'Published at',
      type: 'datetime',
    }),
    {
      name: 'featured',
      title: 'Mark as Featured',
      type: 'boolean',
      initialValue: false,
    },
    defineField({
      name: 'body',
      title: 'Body',
      type: 'blockContent',
    }),
    defineField({
      name: 'metaTitle',
      title: 'Meta Title',
      type: 'string',
      description: 'Title for search engines (max 60 characters).',
      validation: (Rule) => Rule.max(60),
    }),
    defineField({
      name: 'metaDescription',
      title: 'Meta Description',
      type: 'string',
      description: 'Short description for search engines (max 160 characters).',
      validation: (Rule) => Rule.max(160),
    }),
  ],
  preview: {
    select: {
      title: 'title',
      author: 'author.name',
      media: 'mainImage',
      publishedAt: 'publishedAt',
      featured: 'featured',
    },
    prepare(selection) {
      const { author, publishedAt, featured } = selection
      return {
        ...selection,
        subtitle: [
          author ? `by ${author}` : '',
          new Date(publishedAt).toLocaleDateString(),
          featured ? '⭐' : '',
        ]
          .filter(Boolean) // Remove empty strings
          .join(' | ') // Join non-empty parts with separator
          .replace(/\s+\|/, ' |') // Clean up any space before pipe
          .replace(/\| $/, ''), // Remove trailing pipe
      }
    },
  },
})
