import { defineQuery } from 'next-sanity'
export const PRODUCT_LISTING_QUERY = defineQuery(`
  *[_type == "product" && (
    $category == "All" || productCategory->name == $category
  ) && (
    $search == "*" || name match $search || description match $search
  ) && (
    $country == "All" || $country in supportedCountries
  )] {
    _id,
    name,
    slug,
    mainImage {
      asset -> {
        _id,
        url
      }
    },
    price,
    currencySymbol,
    "productCategory": productCategory->name,
    "categoryColor": productCategory->color,
    supportedCountries,
    description,
    affiliateLink,
    retailer,
    popularity,
    creationDate
  }
`)

export const PRODUCT_DETAIL_QUERY = defineQuery(`
  *[_type == "product" && slug.current == $slug][0] {
    _id,
    name,
    slug,
    mainImage {
      asset -> {
        _id,
        url
      }
    },
    productImages[] {
      asset -> {
        _id,
        url
      }
    },
    price,
    currencySymbol,
    "productCategory": productCategory->name,
    supportedCountries,
    description,
    specifications,
    affiliateLink,
    retailer,
    popularity,
    creationDate,
    "relatedProducts": *[_type == "product" && productCategory._ref == ^.productCategory._ref && _id != ^._id] {
      _id,
      name,
      slug,
      mainImage {
        asset -> {
          _id,
          url
        }
      },
      price,
      currencySymbol,
      retailer,
      supportedCountries
    }
  }
`)

export const CATEGORIES_QUERY = defineQuery(`
  *[_type == "productCategory" && isActive == true] | order(sortOrder asc) {
    name,
    color,
    icon,
    sortOrder
  }
`)

export const WISHLIST_QUERY = defineQuery(`
  *[_type == "product" && _id in $productIds] {
    _id,
    name,
    slug,
    mainImage {
      asset -> {
        _id,
        url
      }
    },
    price,
    currencySymbol,
    "productCategory": productCategory->name,
    affiliateLink,
    retailer
  }
`)

export const SETTINGS_QUERY = defineQuery(`*[_type == "settings"][0]`)
