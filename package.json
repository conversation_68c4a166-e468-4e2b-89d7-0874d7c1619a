{"name": "amazon-products", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^4.4.0", "@supabase/supabase-js": "^2.55.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "next": "15.4.6", "next-auth": "^5.0.0-beta.29", "next-sanity": "^10.0.10", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "sanity": "^4.4.0", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}