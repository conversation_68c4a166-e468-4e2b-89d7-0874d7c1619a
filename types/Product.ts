// export interface Product {
//   _id: string
//   _createdAt: string
//   _updatedAt: string
//   title: string
//   slug: { current: string }
//   description: string
//   mainImage: {
//     alt: string
//     asset: { _ref: string; url: string }
//     blurDataURL?: string
//   }
//   price: number
//   affiliateLink: string
//   platform: string
//   categories: { _id: string; title: string; slug: { current: string } }[]
//   metaTitle?: string
//   metaDescription?: string
// }


export interface Product {
  _id: string
  name: string
  slug: {
    current: string
  }
  mainImage?: {
    asset: {
      _id: string
      url: string
    }
  }
  productImages?: Array<{
    asset: {
      _id: string
      url: string
    }
  }>
  price: number
  currencySymbol: string
  productCategory: string
  supportedCountries: string[]
  description?: string
  specifications?: Array<{key: string, value: string}> | any[] // Key-value pairs from scraper or PortableText blocks
  affiliateLink?: string
  retailer: string
  popularity?: number
  creationDate: string
  relatedProducts?: Product[]
}

export interface WishlistItem {
  productId: string
  addedAt: string
}

export interface Country {
  name: string
  code: string
  currency: string
}

export interface SortOption {
  label: string
  value: string
}

export interface ProductListingResponse {
  products: Product[]
  hasMore: boolean
  total: number
}

export interface CategoriesResponse {
  categories: string[]
}
