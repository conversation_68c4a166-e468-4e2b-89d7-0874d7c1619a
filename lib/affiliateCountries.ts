import type { Country } from '@/types/Product'

export const SUPPORTED_COUNTRIES: Country[] = [
  { name: 'United States', code: 'US', currency: 'USD' },
  { name: 'Canada', code: 'CA', currency: 'CAD' },
  { name: 'Brazil', code: 'BR', currency: 'BRL' },
  { name: 'Mexico', code: 'MX', currency: 'MXN' },
  { name: 'United Kingdom', code: 'GB', currency: 'GBP' },
  { name: 'Germany', code: 'DE', currency: 'EUR' },
  { name: 'France', code: 'FR', currency: 'EUR' },
  { name: 'Italy', code: 'IT', currency: 'EUR' },
  { name: 'Spain', code: 'ES', currency: 'EUR' },
  { name: 'Netherlands', code: 'NL', currency: 'EUR' },
  { name: 'Sweden', code: 'SE', currency: 'SEK' },
  { name: 'Poland', code: 'PL', currency: 'PLN' },
  { name: 'India', code: 'IN', currency: 'INR' },
  { name: 'United Arab Emirates', code: 'AE', currency: 'AED' },
  { name: 'Saudi Arabia', code: 'SA', currency: 'SAR' },
  { name: 'Singapore', code: 'SG', currency: 'SGD' },
  { name: 'Japan', code: 'JP', currency: 'JPY' },
  { name: 'China', code: 'CN', currency: 'CNY' },
  { name: 'Australia', code: 'AU', currency: 'AUD' },
]

export const DEFAULT_COUNTRY = 'India'

export const EU_COUNTRIES = ['DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL']

export const getCountryByName = (name: string): Country | undefined => {
  return SUPPORTED_COUNTRIES.find((country) => country.name === name)
}

export const getCurrencySymbol = (currencyCode: string): string => {
  const symbols: Record<string, string> = {
    USD: '$',
    CAD: 'C$',
    BRL: 'R$',
    MXN: '$',
    GBP: '£',
    EUR: '€',
    SEK: 'kr',
    PLN: 'zł',
    INR: '₹',
    AED: 'د.إ',
    SAR: '﷼',
    SGD: 'S$',
    JPY: '¥',
    CNY: '¥',
    AUD: 'A$',
  }
  return symbols[currencyCode] || currencyCode
}
