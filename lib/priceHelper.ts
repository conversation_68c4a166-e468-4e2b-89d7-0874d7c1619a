import usePriceStore from '@/store/usePriceStore'
import type { Product } from '@/types/Product'

export async function updateProductPrice(product: Product): Promise<number> {
  const { setPrice, setLoading, setError, getPrice, isPriceStale } = usePriceStore.getState()
  
  // Only update for Amazon products with affiliate links
  if (product.retailer !== 'Amazon' || !product.affiliateLink) {
    return product.price
  }

  // Check if we already have recent price data
  const existingPrice = getPrice(product._id)
  if (existingPrice && !isPriceStale(product._id, 5)) {
    return existingPrice.price
  }

  setLoading(product._id, true)
  
  try {
    const response = await fetch('/api/scrape-price', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: product.affiliateLink,
        retailer: product.retailer
      })
    })

    if (!response.ok) {
      throw new Error('Failed to fetch price')
    }

    const data = await response.json()
    
    // Only update if price is different from original
    if (data.price !== product.price) {
      setPrice(product._id, data.price, product.price)
      return data.price
    }
    
    return product.price
    
  } catch (error) {
    console.error('Error updating price:', error)
    setError(product._id, error instanceof Error ? error.message : 'Failed to fetch price')
    return product.price
  }
}
