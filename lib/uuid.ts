import { randomUUID, createHash } from 'crypto'

// Generate a random UUID v4
export function generateUUID(): string {
  return randomUUID()
}

// Validate if a string is a valid UUID
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

// Generate a deterministic UUID based on provider and provider account ID
// This ensures the same Google account always gets the same UUID
export function generateDeterministicUUID(provider: string, providerAccountId: string): string {
  // Create a consistent seed from provider and account ID
  const seed = `${provider}:${providerAccountId}`
  
  // Generate a hash from the seed
  const hash = createHash('sha256').update(seed).digest('hex')
  
  // Format as UUID v4
  const uuid = [
    hash.substring(0, 8),
    hash.substring(8, 12),
    '4' + hash.substring(12, 15), // Version 4
    (8 + (parseInt(hash.substring(15, 16), 16) % 4)).toString(16) + hash.substring(16, 19),
    hash.substring(19, 31)
  ].join('-')
  
  return uuid
}
