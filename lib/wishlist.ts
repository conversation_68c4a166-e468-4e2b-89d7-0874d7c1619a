import { supabaseAdmin } from '@/lib/supabase'
import type { User, WishlistItem } from '@/lib/supabase'

export interface WishlistService {
  getUserWishlist: (userId: string) => Promise<WishlistItem[]>
  addToWishlist: (
    userId: string,
    productId: string
  ) => Promise<WishlistItem | null>
  removeFromWishlist: (userId: string, productId: string) => Promise<boolean>
  isInWishlist: (userId: string, productId: string) => Promise<boolean>
  createOrUpdateUser: (userData: Partial<User>) => Promise<User | null>
}

export const wishlistService: WishlistService = {
  async getUserWishlist(userId: string): Promise<WishlistItem[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('wishlist_items')
        .select('*')
        .eq('user_id', userId)
        .order('added_at', { ascending: false })

      if (error) {
        console.error('Error fetching wishlist:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getUserWishlist:', error)
      return []
    }
  },

  async addToWishlist(
    userId: string,
    productId: string
  ): Promise<WishlistItem | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('wishlist_items')
        .insert({
          user_id: userId,
          product_id: productId,
        })
        .select()
        .single()

      if (error) {
        console.error('Error adding to wishlist:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in addToWishlist:', error)
      return null
    }
  },

  async removeFromWishlist(
    userId: string,
    productId: string
  ): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('wishlist_items')
        .delete()
        .eq('user_id', userId)
        .eq('product_id', productId)

      if (error) {
        console.error('Error removing from wishlist:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in removeFromWishlist:', error)
      return false
    }
  },

  async isInWishlist(userId: string, productId: string): Promise<boolean> {
    try {
      const { data, error } = await supabaseAdmin
        .from('wishlist_items')
        .select('id')
        .eq('user_id', userId)
        .eq('product_id', productId)
        .single()

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned"
        console.error('Error checking wishlist:', error)
        return false
      }

      return !!data
    } catch (error) {
      console.error('Error in isInWishlist:', error)
      return false
    }
  },

  async createOrUpdateUser(userData: Partial<User>): Promise<User | null> {
    try {
      if (!userData.id || !userData.email) {
        console.error('Missing required user data (id or email)')
        return null
      }

      // First, try to find user by ID
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', userData.id)
        .single()

      if (existingUser) {
        // User exists by ID, update it
        const { data, error } = await supabaseAdmin
          .from('users')
          .update({
            email: userData.email,
            name: userData.name,
            avatar_url: userData.avatar_url,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userData.id)
          .select()
          .single()

        if (error) {
          console.error('Error updating user by ID:', error)
          return null
        }
        return data
      }

      // If not found by ID, try to find by email (in case of ID mismatch)
      const { data: userByEmail } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', userData.email)
        .single()

      if (userByEmail) {
        // User exists by email but with different ID
        // Update the existing user with the new ID and other data
        const { data, error } = await supabaseAdmin
          .from('users')
          .update({
            id: userData.id, // Update to the new ID from NextAuth
            name: userData.name,
            avatar_url: userData.avatar_url,
            updated_at: new Date().toISOString(),
          })
          .eq('email', userData.email)
          .select()
          .single()

        if (error) {
          console.error('Error updating user by email:', error)
          // If updating ID fails due to constraints, return the existing user
          return userByEmail
        }
        return data
      }

      // User doesn't exist at all, create a new one
      const { data, error } = await supabaseAdmin
        .from('users')
        .insert({
          id: userData.id,
          email: userData.email,
          name: userData.name || null,
          avatar_url: userData.avatar_url || null,
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating user:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in createOrUpdateUser:', error)
      return null
    }
  },
}

// Hook for client-side usage
export function useWishlist() {
  return wishlistService
}
