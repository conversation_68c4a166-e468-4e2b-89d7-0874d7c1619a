import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { DEFAULT_COUNTRY } from '@/lib/affiliateCountries'

interface CountryState {
  selectedCountry: string
  setSelectedCountry: (country: string) => void
  isInitialized: boolean
  initializeCountry: (country?: string) => void
}

const useCountryStore = create<CountryState>()(
  persist(
    (set, get) => ({
      selectedCountry: DEFAULT_COUNTRY,
      isInitialized: false,
      setSelectedCountry: (country) => {
        set({ selectedCountry: country, isInitialized: true })
      },
      initializeCountry: (country) => {
        if (!get().isInitialized) {
          set({ 
            selectedCountry: country || DEFAULT_COUNTRY, 
            isInitialized: true 
          })
        }
      },
    }),
    {
      name: 'country-storage',
      partialize: (state) => ({ selectedCountry: state.selectedCountry }),
    }
  )
)

export default useCountryStore

