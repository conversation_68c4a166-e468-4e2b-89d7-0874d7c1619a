import { create } from 'zustand'

interface LivePrice {
  productId: string
  price: number
  originalPrice: number
  lastUpdated: number
  isLoading: boolean
  error?: string
}

interface PriceState {
  prices: Record<string, LivePrice>
  
  // Actions
  setPrice: (productId: string, price: number, originalPrice: number) => void
  setLoading: (productId: string, isLoading: boolean) => void
  setError: (productId: string, error: string) => void
  getPrice: (productId: string) => LivePrice | undefined
  isPriceStale: (productId: string, maxAgeMinutes?: number) => boolean
  clearPrice: (productId: string) => void
  clearAll: () => void
}

const usePriceStore = create<PriceState>()((set, get) => ({
  prices: {},

  setPrice: (productId: string, price: number, originalPrice: number) => {
    set(state => ({
      prices: {
        ...state.prices,
        [productId]: {
          productId,
          price,
          originalPrice,
          lastUpdated: Date.now(),
          isLoading: false,
          error: undefined
        }
      }
    }))
  },

  setLoading: (productId: string, isLoading: boolean) => {
    set(state => ({
      prices: {
        ...state.prices,
        [productId]: {
          ...state.prices[productId],
          productId,
          price: state.prices[productId]?.price || 0,
          originalPrice: state.prices[productId]?.originalPrice || 0,
          lastUpdated: state.prices[productId]?.lastUpdated || Date.now(),
          isLoading,
          error: undefined
        }
      }
    }))
  },

  setError: (productId: string, error: string) => {
    set(state => ({
      prices: {
        ...state.prices,
        [productId]: {
          ...state.prices[productId],
          productId,
          price: state.prices[productId]?.price || 0,
          originalPrice: state.prices[productId]?.originalPrice || 0,
          lastUpdated: state.prices[productId]?.lastUpdated || Date.now(),
          isLoading: false,
          error
        }
      }
    }))
  },

  getPrice: (productId: string) => {
    return get().prices[productId]
  },

  isPriceStale: (productId: string, maxAgeMinutes: number = 5) => {
    const price = get().prices[productId]
    if (!price) return true
    
    const ageMinutes = (Date.now() - price.lastUpdated) / (1000 * 60)
    return ageMinutes > maxAgeMinutes
  },

  clearPrice: (productId: string) => {
    set(state => {
      const newPrices = { ...state.prices }
      delete newPrices[productId]
      return { prices: newPrices }
    })
  },

  clearAll: () => {
    set({ prices: {} })
  }
}))

export default usePriceStore
