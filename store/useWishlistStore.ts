import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { addToWishlist, removeFromWishlist, getWishlistState, toggleWishlist } from '@/app/actions/wishlist'
import { startTransition } from 'react'

export interface WishlistState {
  items: string[]
  isLoading: boolean
  hasHydrated: boolean
  
  // Actions
  addItem: (productId: string) => Promise<void>
  removeItem: (productId: string) => Promise<void>
  toggleItem: (productId: string) => Promise<void>
  refreshFromServer: () => Promise<void>
  setHasHydrated: () => void
  setItems: (items: string[]) => void
}

const useWishlistStore = create<WishlistState>()(
  persist(
    (set, get) => ({
      items: [],
      isLoading: false,
      hasHydrated: false,

      addItem: async (productId: string) => {
        const state = get()
        
        // Optimistic update wrapped in startTransition
        startTransition(() => {
          if (!state.items.includes(productId)) {
            set({ items: [...state.items, productId], isLoading: true })
          }
        })
        
        try {
          // Call server action
          const result = await addToWishlist(productId)
          startTransition(() => {
            set({ items: result.items })
          })
        } catch (error) {
          console.error('Error adding to wishlist:', error)
          // Revert optimistic update
          const currentState = get()
          startTransition(() => {
            set({ items: currentState.items.filter(id => id !== productId) })
          })
        } finally {
          startTransition(() => {
            set({ isLoading: false })
          })
        }
      },

      removeItem: async (productId: string) => {
        const state = get()
        
        // Optimistic update wrapped in startTransition
        const newItems = state.items.filter(id => id !== productId)
        startTransition(() => {
          set({ items: newItems, isLoading: true })
        })
        
        try {
          // Call server action
          const result = await removeFromWishlist(productId)
          startTransition(() => {
            set({ items: result.items })
          })
        } catch (error) {
          console.error('Error removing from wishlist:', error)
          // Revert optimistic update
          startTransition(() => {
            set({ items: [...state.items, productId] })
          })
        } finally {
          startTransition(() => {
            set({ isLoading: false })
          })
        }
      },

      toggleItem: async (productId: string) => {
        set({ isLoading: true })
        
        try {
          // Call server action
          const result = await toggleWishlist(productId)
          set({ items: result.items })
        } catch (error) {
          console.error('Error toggling wishlist:', error)
        } finally {
          set({ isLoading: false })
        }
      },

      refreshFromServer: async () => {
        set({ isLoading: true })
        
        try {
          const result = await getWishlistState()
          set({ items: result.items })
        } catch (error) {
          console.error('Error refreshing from server:', error)
        } finally {
          set({ isLoading: false })
        }
      },

      setItems: (items: string[]) => {
        set({ items })
      },

      setHasHydrated: () => {
        set({ hasHydrated: true })
      },
    }),
    {
      name: 'wishlist-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ 
        items: state.items
      }),
      onRehydrateStorage: () => (state) => {
        state?.setHasHydrated()
      },
    }
  )
)

export default useWishlistStore
