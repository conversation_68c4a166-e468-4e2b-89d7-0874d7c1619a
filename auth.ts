import <PERSON>Auth from "next-auth"
import Google from "next-auth/providers/google"
import { NextAuthConfig } from "next-auth"
import { generateDeterministicUUID } from "@/lib/uuid"

const config: NextAuthConfig = {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async session({ session, token }) {
      // Add custom fields to session if needed
      if (token.sub) {
        session.user.id = token.sub
        session.user.email = token.email as string
        session.user.name = token.name as string
        session.user.image = token.picture as string
      }
      
      return session
    },
    async jwt({ token, user, account }) {
      // Generate a consistent UUID based on the provider and account ID
      if (account && user) {
        if (account.provider === 'google') {
          // Generate a deterministic UUID based on Google account ID
          token.sub = generateDeterministicUUID('google', account.providerAccountId)
        } else {
          // For other providers, generate UUID based on provider and account ID
          token.sub = generateDeterministicUUID(account.provider, account.providerAccountId)
        }
        
        // Store additional user info in token
        token.email = user.email
        token.name = user.name
        token.picture = user.image
      }
      return token
    },
    async signIn({ user, account }) {
      try {
        console.log('Sign-in callback triggered for user:', user.email)
        console.log('Account provider:', account?.provider)
        console.log('Provider account ID:', account?.providerAccountId)
        
        // The actual sync will happen in the session callback
        // where we have access to the consistent token.sub
      } catch (error) {
        console.error('Error during sign-in sync:', error)
        // Don't block sign-in if sync fails
      }
      return true
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    },
  },
  session: {
    strategy: "jwt",
  },
  trustHost: true,
}

export const { handlers, auth, signIn, signOut } = NextAuth(config)
