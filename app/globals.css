@import 'tailwindcss';
@plugin '@tailwindcss/typography';
@plugin '@tailwindcss/forms';

/* Safelist utilities for Sanity components or cookie-banner */
@source inline(".prose-invert", ".text-shadow-sm", ".mask-image-linear", ".wrap-break-word");

/* Custom theme settings with OKLCH colors */
@theme {
  /* Border radii */
  --radius: 0.5rem;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  /* Colors (OKLCH for modern displays, with HSL fallbacks) */
  --color-brand-primary: oklch(0.55 0.08 260);
  --color-background: oklch(var(--background));
  --color-foreground: oklch(var(--foreground));
  --color-card: oklch(var(--card));
  --color-card-foreground: oklch(var(--card-foreground));
  --color-primary: oklch(var(--primary));
  --color-primary-foreground: oklch(var(--primary-foreground));
  --color-secondary: oklch(var(--secondary));
  --color-secondary-foreground: oklch(var(--secondary-foreground));
  --color-muted: oklch(var(--muted));
  --color-muted-foreground: oklch(var(--muted-foreground));
  --color-accent: oklch(var(--accent));
  --color-accent-foreground: oklch(var(--accent-foreground));
  --color-destructive: oklch(var(--destructive));
  --color-destructive-foreground: oklch(var(--destructive-foreground));
  --color-border: oklch(var(--border));
  --color-input: oklch(var(--input));
  --color-ring: oklch(var(--ring));

  /* Spacing scale for dynamic utilities */
  --spacing: 0.25rem;
}

/* Typography dark mode styles with text-shadow */
@theme {
  --typography-dark: {
    color: oklch(var(--foreground));
    --tw-prose-body: oklch(var(--foreground));
    --tw-prose-headings: oklch(var(--foreground));
    --tw-prose-links: oklch(var(--primary));
    --tw-prose-code: oklch(var(--accent-foreground));
    --tw-prose-quotes: oklch(var(--muted-foreground));
  };
}

.dark .prose {
  @apply prose-invert;
}

/* Compatibility styles for border color */
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: oklch(var(--border));
  }
}

/* TailwindCSS v4 dark mode variant */
@variant dark (.dark &);

@utility btn-primary {
  background-color: rgb(79 70 229);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;

  &:hover {
    background-color: rgb(67 56 202);
  }

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow:
      0 0 0 2px rgb(99 102 241),
      0 0 0 4px rgb(165 180 252);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@utility btn-secondary {
  background-color: rgb(229 231 235);
  color: rgb(17 24 39);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;

  &:hover {
    background-color: rgb(209 213 219);
  }

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow:
      0 0 0 2px rgb(107 114 128),
      0 0 0 4px rgb(59 130 246);
  }

  .dark & {
    background-color: rgb(55 65 81);
    color: rgb(243 244 246);

    &:hover {
      background-color: rgb(75 85 99);
    }
  }
}

@utility card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
  border: 1px solid rgb(229 231 235);

  .dark & {
    background-color: rgb(31 41 55);
    border-color: rgb(55 65 81);
  }
}

@utility input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  background-color: white;
  color: rgb(17 24 39);

  &::placeholder {
    color: rgb(156 163 175);
  }

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px rgb(99 102 241);
    border-color: rgb(99 102 241);
  }

  .dark & {
    border-color: rgb(75 85 99);
    background-color: rgb(31 41 55);
    color: rgb(243 244 246);

    &::placeholder {
      color: rgb(107 114 128);
    }
  }
}

@utility animate-pulse-slow {
  /* Loading animations */
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@utility animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@utility card-hover {
  /* Hover effects for cards */
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;

  &:hover {
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    transform: translateY(-0.25rem);
  }
}

@utility line-clamp-2 {
  /* Text truncation utilities */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@utility line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@utility text-balance {
  text-wrap: balance;
}

@utility scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* Safe area utilities for mobile */
@utility safe-top {
  padding-top: env(safe-area-inset-top);
}

@utility safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

@utility safe-left {
  padding-left: env(safe-area-inset-left);
}

@utility safe-right {
  padding-right: env(safe-area-inset-right);
}

@utility containerMdGrid2 {
  /* Container queries support */
  @container (min-width: 768px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@utility containerLgGrid3 {
  /* Container queries support */
  @container (min-width: 1024px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@layer base {
  /* CSS Variables with OKLCH for better color representation */
  :root {
    --background: 0.99 0 0;
    --foreground: 0.2 0.01 0;
    --card: 0.99 0 0;
    --card-foreground: 0.2 0.01 0;
    --primary: 0.65 0.15 260;
    --primary-foreground: 0.95 0.02 0;
    --secondary: 0.95 0.01 0;
    --secondary-foreground: 0.2 0.01 0;
    --muted: 0.95 0.01 0;
    --muted-foreground: 0.6 0.01 0;
    --accent: 0.95 0.01 0;
    --accent-foreground: 0.2 0.01 0;
    --destructive: 0.6 0.2 0;
    --destructive-foreground: 0.95 0.02 0;
    --border: 0.85 0.01 0;
    --input: 0.85 0.01 0;
    --ring: 0.65 0.15 260;
    --radius: 0.5rem;
    min-height: 100vh;
  }

  .dark {
    --background: 0.15 0.006 285.82;
    --foreground: 0.95 0.02 0;
    --card: 0.25 0.02 225;
    --card-foreground: 0.95 0.02 0;
    --primary: 0.95 0.02 0;
    --primary-foreground: 0.2 0.01 0;
    --secondary: 0.3 0.01 0;
    --secondary-foreground: 0.95 0.02 0;
    --muted: 0.3 0.01 0;
    --muted-foreground: 0.6 0.01 0;
    --accent: 0.3 0.01 0;
    --accent-foreground: 0.95 0.02 0;
    --destructive: 0.4 0.15 0;
    --destructive-foreground: 0.95 0.02 0;
    --border: 0.3 0.01 0;
    --input: 0.3 0.01 0;
    --ring: 0.8 0.1 260;
  }

  * {
    border-color: oklch(var(--border));
  }

  body {
    background-color: oklch(var(--background));
    color: oklch(var(--foreground));
  }

  html {
    scroll-behavior: smooth;
  }

  .cookie-banner {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }
}

@layer components {
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Performance optimizations */
img {
  content-visibility: auto;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .card {
    border: 1px solid ButtonText;
  }

  .btn-primary {
    border: 1px solid ButtonText;
  }

  .input-field {
    border: 1px solid ButtonText;
  }
}
