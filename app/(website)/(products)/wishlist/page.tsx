// app/products/wishlist/page.tsx
import { Metadata } from 'next'
import { Suspense } from 'react'
import {
  getWishlistState,
  getWishlistProductsAction,
} from '@/app/actions/wishlist'
import WishlistPageClient from '@/components/wishlist/WishlistPageClient'
import WishlistSkeleton from '@/components/wishlist/WishlistSkeleton'
import WishlistErrorFallback from '@/components/wishlist/WishlistErrorFallback'

// Force this page to be dynamic since it uses cookies
export const dynamic = 'force-dynamic'
export const revalidate = 0

export const metadata: Metadata = {
  title: 'My Wishlist | Your Store',
  description: 'Your saved products and wishlist items',
  robots: 'noindex, nofollow',
}

export default async function WishlistPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Suspense fallback={<WishlistSkeleton />}>
        <WishlistContent />
      </Suspense>
    </div>
  )
}

async function WishlistContent() {
  try {
    const [wishlistState, wishlistProducts] = await Promise.all([
      getWishlistState(),
      getWishlistProductsAction(),
    ])

    return (
      <WishlistPageClient
        initialWishlistItems={wishlistState.items}
        initialWishlistProducts={wishlistProducts}
        initialWishlistCount={wishlistState.count}
      />
    )
  } catch (error) {
    console.error('Error loading wishlist:', error)
    return <WishlistErrorFallback />
  }
}
