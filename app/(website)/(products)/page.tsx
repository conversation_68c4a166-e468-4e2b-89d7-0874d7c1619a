import { Suspense } from 'react'
import { Metadata } from 'next'
import { getProducts, getCategories } from '@/app/actions/products'
import { DEFAULT_COUNTRY } from '@/lib/affiliateCountries'
import ProductListingClient from '@/components/products/ProductListingClient'
import ProductListingSkeleton from '@/components/products/ProductListingSkeleton'

interface ProductsPageProps {
  searchParams: Promise<{
    country?: string
    category?: string
    search?: string
    sort?: string
    page?: string
  }>
}

export const metadata: Metadata = {
  title: 'Products - Your Store',
  description: 'Discover amazing products from top retailers',
}

const PRODUCTS_PER_PAGE = 20

export default async function ProductsPage({
  searchParams,
}: ProductsPageProps) {
  const params = await searchParams
  const {
    country = DEFAULT_COUNTRY,
    category = 'All',
    search = '',
    sort = 'creationDate-desc',
    page = '1',
  } = params

  const [sortField, sortOrder] = sort.split('-')
  const currentPage = parseInt(page) || 1
  const offset = (currentPage - 1) * PRODUCTS_PER_PAGE

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Suspense fallback={<ProductListingSkeleton />}>
        <ProductListingContent
          country={country}
          category={category}
          search={search}
          sortField={sortField}
          sortOrder={sortOrder}
          offset={offset}
          limit={PRODUCTS_PER_PAGE}
          shouldRedirectForDefaultCountry={!params.country}
        />
      </Suspense>
    </div>
  )
}

interface ProductListingContentProps {
  country: string
  category: string
  search: string
  sortField: string
  sortOrder: string
  offset: number
  limit: number
  shouldRedirectForDefaultCountry: boolean
}

async function ProductListingContent({
  country,
  category,
  search,
  sortField,
  sortOrder,
  offset,
  limit,
  shouldRedirectForDefaultCountry,
}: ProductListingContentProps) {
  try {
    const [productsData, categoriesData] = await Promise.all([
      getProducts({
        country,
        category,
        search,
        sortField,
        sortOrder,
        offset,
        limit,
      }),
      getCategories(),
    ])

    return (
      <ProductListingClient
        initialProducts={productsData.products}
        initialCategories={categoriesData.categories}
        initialHasMore={productsData.hasMore}
        initialParams={{
          country,
          category,
          search,
          sort: `${sortField}-${sortOrder}`,
        }}
        shouldRedirectForDefaultCountry={shouldRedirectForDefaultCountry}
      />
    )
  } catch (error) {
    console.error('Error loading products:', error)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Something went wrong
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            We couldn&apos;t load the products. Please try again later.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }
}
