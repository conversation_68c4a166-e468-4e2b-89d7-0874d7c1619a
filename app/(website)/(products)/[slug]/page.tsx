// app/products/[slug]/page.tsx
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getProductBySlug } from '@/app/actions/products'
import { getWishlistState } from '@/app/actions/wishlist'
import BreadcrumbNavigation from '@/components/products/BreadcrumbNavigation'
import ProductImageGallery from '@/components/products/ProductImageGallery'
import ProductInformation from '@/components/products/ProductInformation'
import ContentSections from '@/components/products/ContentSections'
import ActionButtons from '@/components/products/ActionButtons'
import RelatedProducts from '@/components/products/RelatedProducts'

// Force dynamic rendering to prevent static-to-dynamic errors
export const dynamic = 'force-dynamic'

interface ProductPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({
  params,
}: ProductPageProps): Promise<Metadata> {
  const { slug } = await params

  try {
    const product = await getProductBySlug(slug)

    if (!product) {
      return {
        title: 'Product Not Found',
        description: 'The product you are looking for does not exist.',
      }
    }

    const pageTitle = `${product.name} | Your Store`
    const pageDescription =
      product.description ||
      `${product.name} - Available from ${product.retailer}`
    const productImage = product.mainImage?.asset?.url

    return {
      title: pageTitle,
      description: pageDescription,
      openGraph: {
        type: 'website',
        title: pageTitle,
        description: pageDescription,
        images: productImage ? [{ url: productImage }] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: pageTitle,
        description: pageDescription,
        images: productImage ? [productImage] : [],
      },
    }
  } catch (error) {
    console.error('Error generating metadata for product:', error)
    return {
      title: 'Product Not Found',
      description: 'The product you are looking for does not exist.',
    }
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { slug } = await params

  try {
    const [product, wishlistState] = await Promise.all([
      getProductBySlug(slug),
      getWishlistState(),
    ])

    if (!product) {
      notFound()
    }

    // Generate structured data for SEO
    const structuredData = {
      '@context': 'https://schema.org/',
      '@type': 'Product',
      name: product.name,
      description: product.description,
      image: product.mainImage?.asset?.url,
      brand: {
        '@type': 'Brand',
        name: product.retailer,
      },
      offers: {
        '@type': 'Offer',
        price: product.price,
        priceCurrency: product.currencySymbol,
        availability: 'https://schema.org/InStock',
        url: product.affiliateLink,
      },
      category: product.productCategory,
    }

    return (
      <>
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />

        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          {/* Sticky Breadcrumb Navigation */}
          <div className="sticky top-16 z-40 bg-gray-50/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
              <BreadcrumbNavigation product={product} />
            </div>
          </div>

          {/* Scrollable Main Content */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Main Product Content */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-12">
              {/* Product Images */}
              <ProductImageGallery product={product} />

              {/* Product Information and Actions */}
              <div className="space-y-8">
                <ProductInformation product={product} />
                <ActionButtons
                  product={product}
                  initialIsWishlisted={wishlistState.items.includes(
                    product._id
                  )}
                />
              </div>
            </div>

            {/* Content Sections */}
            <ContentSections product={product} className="mb-12" />

            {/* Related Products */}
            <RelatedProducts relatedProducts={product.relatedProducts} />
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading product:', error)
    notFound()
  }
}

// Generate static params for popular products
export async function generateStaticParams() {
  // You could fetch popular product slugs here
  // For now, return empty array to enable ISR for all products
  return []
}
