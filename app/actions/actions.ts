'use server'

import { sanityFetch } from '@/sanity/lib/live'
import { PAGINATED_POSTS_QUERY, PRODUCTS_QUERY } from '@/sanity/lib/groq'

export async function fetchPosts(
  lastPublishedAt: string | null = null,
  lastId: string | null = null
) {
  const { data: posts } = await sanityFetch({
    query: PAGINATED_POSTS_QUERY,
    params: { lastPublishedAt, lastId },
  })
  return posts
}

export async function fetchProducts(lastId: string | null = null) {
  const { data: products } = await sanityFetch({
    query: PRODUCTS_QUERY,
    params: { lastId },
  })
  return products
}
