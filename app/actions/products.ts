
'use server'

import {
  PRODUCT_LISTING_QUERY,
  PRODUCT_DETAIL_QUERY,
  CATEGORIES_QUERY,
  WISHLIST_QUERY,
} from '@/sanity/lib/groq'
import { sanityFetch } from '@/sanity/lib/live'
import type {
  Product,
  ProductListingResponse,
  CategoriesResponse,
} from '@/types/Product'
import { revalidatePath } from 'next/cache'

interface GetProductsParams {
  country?: string
  category?: string
  search?: string
  sortField?: string
  sortOrder?: string
  offset?: number
  limit?: number
}

export async function getProducts(
  params: GetProductsParams = {}
): Promise<ProductListingResponse> {
  const {
    category = 'All',
    search = '',
    sortField = 'creationDate',
    sortOrder = 'desc',
    offset = 0,
    limit = 20,
  } = params

  try {
    const queryParams = {
      category: category || 'All',
      search: search ? `*${search}*` : '*',
      country: params.country || 'All'
    }

    const { data: products } = await sanityFetch({
      query: PRODUCT_LISTING_QUERY,
      params: queryParams
    })

    const productArray = products || []
    
    // Apply sorting in JavaScript (more reliable than complex GROQ ordering)
    productArray.sort((a: Product, b: Product) => {
      if (sortField === 'price') {
        return sortOrder === 'asc' ? a.price - b.price : b.price - a.price
      }
      if (sortField === 'name') {
        return sortOrder === 'asc'
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name)
      }
      if (sortField === 'popularity') {
        return sortOrder === 'asc'
          ? (a.popularity || 0) - (b.popularity || 0)
          : (b.popularity || 0) - (a.popularity || 0)
      }
      // Default: sort by creation date
      const aDate = new Date(a.creationDate || '1970-01-01').getTime()
      const bDate = new Date(b.creationDate || '1970-01-01').getTime()
      return sortOrder === 'asc' ? aDate - bDate : bDate - aDate
    })
    
    // Apply pagination after sorting
    const totalProducts = productArray.length
    const startIndex = offset
    const endIndex = offset + limit
    const paginatedProducts = productArray.slice(startIndex, endIndex)
    const hasMore = endIndex < totalProducts

    return {
      products: paginatedProducts,
      hasMore,
      total: totalProducts,
    }
  } catch (error) {
    console.error('Error fetching products:', error)
    // Return empty result instead of throwing to prevent app crashes
    return {
      products: [],
      hasMore: false,
      total: 0,
    }
  }
}

export async function getProductBySlug(slug: string): Promise<Product | null> {
  try {
    const { data: product } = await sanityFetch({
      query: PRODUCT_DETAIL_QUERY,
      params: { slug }
    });

    return product || null;
  } catch (error) {
    console.error('Error fetching product details:', error);
    throw new Error('Failed to fetch product details');
  }
}

export async function getCategories(): Promise<CategoriesResponse> {
  try {
    const result = await sanityFetch({ query: CATEGORIES_QUERY })
    const categoryNames = (result.data || []).map((item: { name: string }) => item.name)
    
    // Always include 'All' as the first option
    const categories = ['All', ...categoryNames]

    return { categories }
  } catch (error) {
    console.error('Error fetching categories:', error)
    // Return default categories as fallback
    return {
      categories: ['All', 'Electronics', 'Home & Garden', 'Supplements', 'Vitamins', 'Protein', 'Fitness Equipment', 'Health Food', 'Wellness']
    }
  }
}

export async function getWishlistProducts(
  productIds: string[]
): Promise<Product[]> {
  if (productIds.length === 0) {
    return []
  }

  try {
    const { data: products } = await sanityFetch({
      query: WISHLIST_QUERY,
      params: { productIds }
    })

    return products || []
  } catch (error) {
    console.error('Error fetching wishlist products:', error)
    throw new Error('Failed to fetch wishlist products')
  }
}

// Revalidate product pages when needed
export async function revalidateProducts() {
  revalidatePath('/products')
  revalidatePath('/products/[slug]', 'page')
}

export async function revalidateProduct(slug: string) {
  revalidatePath(`/products/${slug}`)
}
