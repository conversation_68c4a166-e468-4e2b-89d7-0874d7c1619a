'use server'

import { revalidateTag, revalidatePath } from 'next/cache'

export async function revalidatePost(slug?: string) {
  try {
    // Revalidate specific post if slug is provided
    if (slug) {
      revalidateTag(`post-${slug}`)
      revalidatePath(`/posts/${slug}`)
    }
    
    // Revalidate post lists
    revalidateTag('posts')
    revalidatePath('/posts')
    revalidatePath('/')
    
    console.log('Post revalidation completed', { slug })
    return { success: true }
  } catch (error) {
    console.error('Post revalidation failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function revalidateProduct(slug?: string) {
  try {
    // Revalidate specific product if slug is provided
    if (slug) {
      revalidateTag(`product-${slug}`)
      revalidatePath(`/products/${slug}`)
    }
    
    // Revalidate product lists and related pages
    revalidateTag('products')
    revalidatePath('/products')
    revalidatePath('/')
    
    console.log('Product revalidation completed', { slug })
    return { success: true }
  } catch (error) {
    console.error('Product revalidation failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function revalidateAuthor(slug?: string) {
  try {
    // Revalidate specific author if slug is provided
    if (slug) {
      revalidateTag(`author-${slug}`)
      revalidatePath(`/author/${slug}`)
    }
    
    // Revalidate author lists and related posts
    revalidateTag('authors')
    revalidateTag('posts')
    revalidatePath('/')
    
    console.log('Author revalidation completed', { slug })
    return { success: true }
  } catch (error) {
    console.error('Author revalidation failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function revalidateCategory(slug?: string) {
  try {
    // Revalidate specific category if slug is provided
    if (slug) {
      revalidateTag(`category-${slug}`)
      revalidatePath(`/category/${slug}`)
    }
    
    // Revalidate category lists and related posts
    revalidateTag('categories')
    revalidateTag('posts')
    revalidatePath('/')
    
    console.log('Category revalidation completed', { slug })
    return { success: true }
  } catch (error) {
    console.error('Category revalidation failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function revalidateProductCategory(slug?: string) {
  try {
    // Revalidate specific product category if slug is provided
    if (slug) {
      revalidateTag(`product-category-${slug}`)
    }
    
    // Revalidate product category lists and related products
    revalidateTag('product-categories')
    revalidateTag('products')
    revalidatePath('/products')
    
    console.log('Product category revalidation completed', { slug })
    return { success: true }
  } catch (error) {
    console.error('Product category revalidation failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function sendContentNotification(contentData: {
  type: 'post' | 'product'
  title: string
  slug: string
  author?: string
  description?: string
  price?: number
  currency?: string
}) {
  try {
    console.log('New content notification:', contentData)
    
    // Example integrations you could add:
    // - Email notifications to subscribers
    // - Slack/Discord notifications to team
    // - Push notifications to mobile app
    // - Social media auto-posting
    
    if (contentData.type === 'post') {
      // Handle new post notifications
      console.log(`📝 New post published: ${contentData.title}`)
    } else if (contentData.type === 'product') {
      // Handle new product notifications
      console.log(`🛍️ New product added: ${contentData.title}`)
      if (contentData.price && contentData.currency) {
        console.log(`💰 Price: ${contentData.currency} ${contentData.price}`)
      }
    }
    
    return { success: true }
  } catch (error) {
    console.error('Content notification failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}
