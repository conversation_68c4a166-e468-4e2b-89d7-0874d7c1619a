'use server'

import { z } from 'zod'

const subscribeFormSchema = z.object({
  email: z.string().email('Invalid email address'),
})

type SubscribeFormData = z.infer<typeof subscribeFormSchema>

export async function subscribe(data: SubscribeFormData) {
  const parsedData = subscribeFormSchema.parse(data)
  const { email } = parsedData

  const apiKey = process.env.BUTTONDOWN_API_KEY

  if (!apiKey) {
    throw new Error('API key or username is not set in environment variables.')
  }

  const url = `https://api.buttondown.email/v1/subscribers`

  const options = {
    method: 'POST',
    headers: {
      Authorization: `Token ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email_address: email }),
  }

  try {
    const response = await fetch(url, options)
    const resData = await response.json()

    if (!response.ok) {
      // Extract error message from the response
      const errorMessage =
        resData.detail || resData.error || 'Subscription failed'
      console.error(`Subscription failed: ${JSON.stringify(resData, null, 2)}`)
      throw new Error(errorMessage)
    }

    return {
      success: true,
      message: 'Successfully subscribed!',
      data: resData,
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error(`Subscription error: ${error.message}`)
      return {
        success: false,
        message: error.message || 'Failed to subscribe',
      }
    }
    console.error(`Unexpected error: ${JSON.stringify(error, null, 2)}`)
    return { success: false, message: 'Unexpected error occurred' }
  }
}
