'use server'

import { auth } from '@/auth'
import { getWishlistProducts } from '@/app/actions/products'
import type { Product } from '@/types/Product'
import { wishlistService } from '@/lib/wishlist'
import { cookies } from 'next/headers'

// Cookie fallback for non-authenticated users
const WISHLIST_COOKIE_NAME = 'wishlist'
const COOKIE_MAX_AGE = 60 * 60 * 24 * 365 // 1 year

interface WishlistState {
  items: string[]
  count: number
}

// Helper function to get user session
async function getCurrentUser() {
  try {
    const session = await auth()
    return session?.user
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
}

// Helper function for cookie-based wishlist (fallback for non-authenticated users)
async function getCookieWishlist(): Promise<string[]> {
  const cookieStore = await cookies()
  const wishlistCookie = cookieStore.get(WISHLIST_COOKIE_NAME)

  if (wishlistCookie?.value) {
    try {
      return JSON.parse(wishlistCookie.value)
    } catch (error) {
      console.error('Error parsing wishlist cookie:', error)
    }
  }
  return []
}

// Helper function to update cookie wishlist
async function setCookieWishlist(items: string[]) {
  const cookieStore = await cookies()
  cookieStore.set(WISHLIST_COOKIE_NAME, JSON.stringify(items), {
    maxAge: COOKIE_MAX_AGE,
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
  })
}

export async function getWishlistState(): Promise<WishlistState> {
  const user = await getCurrentUser()

  if (user?.id) {
    // For authenticated users, get wishlist from database
    const wishlistItems = await wishlistService.getUserWishlist(user.id)
    const items = wishlistItems.map(item => item.product_id)

    return {
      items,
      count: items.length,
    }
  } else {
    // For non-authenticated users, use cookies
    const items = await getCookieWishlist()
    return {
      items,
      count: items.length,
    }
  }
}

export async function addToWishlist(productId: string): Promise<WishlistState> {
  const user = await getCurrentUser()

  if (user?.id) {
    // For authenticated users, ensure user exists in database first
    console.log('Creating/updating user with ID:', user.id, 'Email:', user.email)
    const dbUser = await wishlistService.createOrUpdateUser({
      id: user.id,
      email: user.email || '',
      name: user.name || null,
      avatar_url: user.image || null,
    })

    if (!dbUser) {
      console.error('Failed to create/update user in database')
      // Fall back to cookie-based storage
      const currentItems = await getCookieWishlist()
      if (!currentItems.includes(productId)) {
        const updatedItems = [...currentItems, productId]
        await setCookieWishlist(updatedItems)
        return {
          items: updatedItems,
          count: updatedItems.length,
        }
      }
      return {
        items: currentItems,
        count: currentItems.length,
      }
    }

    // Then add to database
    const wishlistItem = await wishlistService.addToWishlist(user.id, productId)
    if (!wishlistItem) {
      console.error('Failed to add item to wishlist')
    }

    return await getWishlistState()
  } else {
    // For non-authenticated users, use cookies
    const currentItems = await getCookieWishlist()
    if (!currentItems.includes(productId)) {
      const updatedItems = [...currentItems, productId]
      await setCookieWishlist(updatedItems)
      return {
        items: updatedItems,
        count: updatedItems.length,
      }
    }
    return {
      items: currentItems,
      count: currentItems.length,
    }
  }
}

export async function removeFromWishlist(productId: string): Promise<WishlistState> {
  const user = await getCurrentUser()

  if (user?.id) {
    // For authenticated users, remove from database
    await wishlistService.removeFromWishlist(user.id, productId)
    return await getWishlistState()
  } else {
    // For non-authenticated users, use cookies
    const currentItems = await getCookieWishlist()
    const updatedItems = currentItems.filter((id) => id !== productId)
    await setCookieWishlist(updatedItems)
    return {
      items: updatedItems,
      count: updatedItems.length,
    }
  }
}

export async function toggleWishlist(productId: string): Promise<WishlistState> {
  const currentWishlist = await getWishlistState()

  if (currentWishlist.items.includes(productId)) {
    return await removeFromWishlist(productId)
  } else {
    return await addToWishlist(productId)
  }
}

export async function clearWishlist(): Promise<WishlistState> {
  const user = await getCurrentUser()

  if (user?.id) {
    // For authenticated users, clear database wishlist
    const wishlistItems = await wishlistService.getUserWishlist(user.id)
    for (const item of wishlistItems) {
      await wishlistService.removeFromWishlist(user.id, item.product_id)
    }
    return { items: [], count: 0 }
  } else {
    // For non-authenticated users, clear cookies
    await setCookieWishlist([])
    return { items: [], count: 0 }
  }
}

export async function getWishlistProductsAction(): Promise<Product[]> {
  const wishlistState = await getWishlistState()
  return await getWishlistProducts(wishlistState.items)
}

export async function isInWishlist(productId: string): Promise<boolean> {
  const user = await getCurrentUser()

  if (user?.id) {
    return await wishlistService.isInWishlist(user.id, productId)
  } else {
    const items = await getCookieWishlist()
    return items.includes(productId)
  }
}

// New function to sync cookie wishlist to database when user logs in
export async function syncWishlistToDatabase(): Promise<void> {
  const user = await getCurrentUser()

  if (user?.id) {
    const cookieItems = await getCookieWishlist()

    if (cookieItems.length > 0) {
      // Add cookie items to database
      for (const productId of cookieItems) {
        await wishlistService.addToWishlist(user.id, productId)
      }

      // Clear cookie after syncing
      await setCookieWishlist([])
    }
  }
}

// Function to create/update user profile from session
export async function syncUserProfile(): Promise<void> {
  const user = await getCurrentUser()

  if (user) {
    await wishlistService.createOrUpdateUser({
      id: user.id,
      email: user.email || '',
      name: user.name || null,
      avatar_url: user.image || null,
    })
  }
}
