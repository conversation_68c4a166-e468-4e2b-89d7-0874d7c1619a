'use client'

import { useEffect, useState } from 'react'

/**
 * Hook to safely handle hydration and prevent hydration mismatches
 * Returns true only after the component has mounted on the client
 */
export function useHydration() {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    setIsHydrated(true)
  }, [])

  return isHydrated
}

/**
 * Hook to safely access window object after hydration
 */
export function useIsomorphicWindow() {
  const isHydrated = useHydration()
  
  return isHydrated ? window : undefined
}

/**
 * Hook for client-side only rendering with fallback
 */
export function useClientOnly<T>(clientValue: T, serverValue: T) {
  const isHydrated = useHydration()
  
  return isHydrated ? clientValue : serverValue
}
