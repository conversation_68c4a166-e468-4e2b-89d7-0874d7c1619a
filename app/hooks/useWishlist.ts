import { useState, useEffect } from 'react'

const WISHLIST_STORAGE_KEY = 'product-wishlist'

interface UseWishlistReturn {
  wishlist: string[]
  addToWishlist: (productId: string) => void
  removeFromWishlist: (productId: string) => void
  toggleWishlist: (productId: string) => void
  isInWishlist: (productId: string) => boolean
  clearWishlist: () => void
  isLoading: boolean
  wishlistCount: number
}

export const useWishlist = (): UseWishlistReturn => {
  const [wishlist, setWishlist] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Load wishlist from localStorage on mount
  useEffect(() => {
    const loadWishlist = () => {
      try {
        const stored = localStorage.getItem(WISHLIST_STORAGE_KEY)
        if (stored) {
          setWishlist(JSON.parse(stored))
        }
      } catch (error) {
        console.error('Error loading wishlist from localStorage:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadWishlist()
  }, [])

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem(WISHLIST_STORAGE_KEY, JSON.stringify(wishlist))
      } catch (error) {
        console.error('Error saving wishlist to localStorage:', error)
      }
    }
  }, [wishlist, isLoading])

  const addToWishlist = (productId: string): void => {
    const previousWishlist = [...wishlist]
    setWishlist(prev => {
      if (!prev.includes(productId)) {
        return [...prev, productId]
      }
      return prev
    })
    
    // Save to localStorage with error handling
    try {
      const newWishlist = wishlist.includes(productId) ? wishlist : [...wishlist, productId]
      localStorage.setItem(WISHLIST_STORAGE_KEY, JSON.stringify(newWishlist))
    } catch (error) {
      console.error('Error saving to localStorage:', error)
      // Revert on error
      setWishlist(previousWishlist)
    }
  }

  const removeFromWishlist = (productId: string): void => {
    const previousWishlist = [...wishlist]
    setWishlist(prev => prev.filter(id => id !== productId))
    
    // Save to localStorage with error handling
    try {
      const newWishlist = wishlist.filter(id => id !== productId)
      localStorage.setItem(WISHLIST_STORAGE_KEY, JSON.stringify(newWishlist))
    } catch (error) {
      console.error('Error saving to localStorage:', error)
      // Revert on error
      setWishlist(previousWishlist)
    }
  }

  const toggleWishlist = (productId: string): void => {
    if (wishlist.includes(productId)) {
      removeFromWishlist(productId)
    } else {
      addToWishlist(productId)
    }
  }

  const isInWishlist = (productId: string): boolean => {
    return wishlist.includes(productId)
  }

  const clearWishlist = (): void => {
    setWishlist([])
  }

  return {
    wishlist,
    addToWishlist,
    removeFromWishlist,
    toggleWishlist,
    isInWishlist,
    clearWishlist,
    isLoading,
    wishlistCount: wishlist.length
  }
}
