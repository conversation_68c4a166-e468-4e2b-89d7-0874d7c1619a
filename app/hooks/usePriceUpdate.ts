import { useState, useEffect } from 'react'
import { updateProductPrice } from '@/lib/priceHelper'
import usePriceStore from '@/store/usePriceStore'
import type { Product } from '@/types/Product'

export function usePriceUpdate(product: Product) {
  const [currentPrice, setCurrentPrice] = useState(product.price)

  useEffect(() => {
    const updatePrice = async () => {
      const newPrice = await updateProductPrice(product)
      setCurrentPrice(newPrice)
    }

    // Only run for Amazon products
    if (product.retailer === 'Amazon' && product.affiliateLink) {
      updatePrice()
    }
  }, [product])

  // Subscribe to store updates
  useEffect(() => {
    const unsubscribe = usePriceStore.subscribe((state) => {
      const priceData = state.prices[product._id]
      if (priceData && priceData.price !== currentPrice) {
        setCurrentPrice(priceData.price)
      }
    })

    return unsubscribe
  }, [product._id, currentPrice])

  return currentPrice
}
