import type { Metadata } from 'next'
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import '@/app/globals.css'
import Navbar from '@/components/Navbar'
import { SETTINGS_QUERY } from '@/sanity/lib/groq'
import { sanityFetch } from '@/sanity/lib/live'
import Footer from '@/components/Footer'
import { Providers } from '@/app/providers'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Amazon Products',
  description: 'Curated list of Amazon products',
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { data: settings } = await sanityFetch({ query: SETTINGS_QUERY })

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          <Navbar settings={settings} />
          {children}
          <Footer settings={settings} />
        </Providers>
      </body>
    </html>
  )
}
