'use client'

import Container from '@/components/Container'
// import SubscribeForm from '@/components/forms/subscribe'
import Link from 'next/link'
import Image from 'next/image'
import { Setting } from '@/types/Setting'
import { urlFor } from '@/sanity/lib/image'

interface FooterProps {
  readonly settings: Readonly<Setting>
}

export default function Footer({ settings }: FooterProps) {
  const handleManageCookies = () => {
    window.dispatchEvent(new Event('manageCookies'))
  }

  return (
    <footer className="mt-16 lg:mt-20 p-6 bg-gray-50 dark:bg-gray-900 py-10">
      <Container className="w-full max-w-screen-xl mx-auto space-y-8">
        <div className="flex flex-col items-center justify-center space-y-4">
          {/* TODO: Update this section */}
          <Image
            alt="Logo of My Website"
            src={settings?.logo ? urlFor(settings.logo).url() : '/logo.png'}
            width={44}
            height={44}
            className="rounded-md"
          />
          <Link
            href="/"
            className="text-3xl font-semibold text-gray-900 dark:text-white"
          >
            {settings?.siteTitle ?? 'Curated Products'}
          </Link>

          <p className="text-center text-gray-500 dark:text-gray-400 text-sm">
            {settings?.siteDescription ?? 'Brief description of the website.'}
          </p>
        </div>

        <ul className="flex flex-wrap justify-center items-center space-x-6 text-gray-900 dark:text-white text-base">
          <li>
            <Link href="/about" className="hover:underline">
              About
            </Link>
          </li>
          <li>
            <Link href="/contact" className="hover:underline">
              Contact
            </Link>
          </li>
          <li>
            <Link href="/archive" className="hover:underline">
              Archive
            </Link>
          </li>
          <li>
            <Link href="/privacy-policy" className="hover:underline">
              Privacy
            </Link>
          </li>
          <li>
            <Link href="/terms-of-service" className="hover:underline">
              Terms Of Service
            </Link>
          </li>
          <li>
            <Link
              onClick={handleManageCookies}
              href="#"
              className="hover:underline cursor-pointer"
            >
              Manage Cookies
            </Link>
          </li>
          <li>
            <Link href="/do-not-sell" className="hover:underline">
              Do Not Sell
            </Link>
          </li>
        </ul>

        <div className="flex flex-col items-center space-y-4">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            © {new Date().getFullYear()}{' '}
            <Link href="/" className="hover:underline">
              {settings?.copyright ?? 'Curated Products'}
            </Link>
            . All Rights Reserved.
          </span>

          {settings?.enableSubscribeForm && (
            <div className="flex justify-center items-center space-x-4">
              {/* <SubscribeForm
                title="Subscribe"
                placeholder="Your Email Address"
              /> */}
            </div>
          )}
        </div>
      </Container>
    </footer>
  )
}
