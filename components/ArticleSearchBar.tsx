"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface ArticleSearchBarProps {
  placeholder?: string;
  className?: string;
}

const ArticleSearchBar: React.FC<ArticleSearchBarProps> = ({ 
  placeholder = "Search articles...", 
  className = "" 
}) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleSearch = () => {
    if (searchValue.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchValue.trim())}`);
      setIsOpen(false);
      setSearchValue("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch();
    } else if (e.key === "Escape") {
      setIsOpen(false);
      setSearchValue("");
    }
  };

  const handleToggle = () => {
    if (isOpen && searchValue.trim()) {
      handleSearch();
    } else {
      setIsOpen(!isOpen);
      if (isOpen) {
        setSearchValue("");
      }
    }
  };

  return (
    <div className={cn("relative flex items-center", className)}>
      {/* Search Button/Icon */}
      <button
        onClick={handleToggle}
        className={cn(
          "p-2 text-gray-300 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-all duration-200",
          {
            "bg-gray-700 text-white": isOpen,
          }
        )}
        aria-label={isOpen ? "Search" : "Open search"}
        title={isOpen ? "Search" : "Open search"}
      >
        {isOpen && searchValue.trim() ? (
          <MagnifyingGlassIcon className="size-5" />
        ) : isOpen ? (
          <XMarkIcon className="size-5" />
        ) : (
          <MagnifyingGlassIcon className="size-5" />
        )}
      </button>

      {/* Search Input Container */}
      <div
        className={cn(
          "absolute top-0 right-0 transition-all duration-300 ease-in-out overflow-hidden z-50",
          {
            "w-0 opacity-0": !isOpen,
            "w-56 sm:w-72 opacity-100": isOpen,
          }
        )}
        style={{
          transform: isOpen ? 'translateX(-100%)' : 'translateX(0)',
          transformOrigin: 'right center',
        }}
      >
        <div className="flex items-center bg-gray-800 rounded-md border border-gray-600 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
          <input
            ref={inputRef}
            type="text"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="flex-1 px-4 py-2 bg-transparent text-gray-200 placeholder-gray-400 focus:outline-none text-sm"
            style={{ minWidth: 0 }} // Prevent flex item from growing beyond container
          />
          {searchValue && (
            <button
              onClick={() => setSearchValue("")}
              className="p-1 mr-2 text-gray-400 hover:text-gray-200 transition-colors"
              aria-label="Clear search"
            >
              <XMarkIcon className="size-4" />
            </button>
          )}
        </div>
      </div>

      {/* Backdrop for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/25 z-[-1] sm:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ArticleSearchBar;

