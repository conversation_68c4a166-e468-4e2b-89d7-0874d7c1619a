'use client'

import { cn } from '@/lib/utils'
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from '@headlessui/react'
import { Bars3Icon, XMarkIcon, HeartIcon, UserIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import Link from 'next/link'
import { useSession, signIn, signOut } from 'next-auth/react'
import useCountryStore from '@/store/useCountryStore'
import ArticleSearchBar from '@/components/ArticleSearchBar'
import ThemeSwitch from '@/components/ThemeSwitch'
import { Setting } from '@/types/Setting'
import { urlFor } from '@/sanity/lib/image'

interface NavbarProps {
  readonly settings: Readonly<Setting>
}

export default function Navbar({ settings }: NavbarProps) {
  const { data: session } = useSession()
  const { selectedCountry } = useCountryStore()

  // Helper function to build URLs with country parameter
  const getProductsUrl = () => {
    if (selectedCountry) {
      return `/products?country=${encodeURIComponent(selectedCountry)}`
    }
    return '/products'
  }

  const getWishlistUrl = () => {
    const baseUrl = '/products/wishlist'
    if (selectedCountry) {
      return `${baseUrl}?country=${encodeURIComponent(selectedCountry)}`
    }
    return baseUrl
  }

  const navigation = [
    { name: 'Home', href: '/', current: true },
    { name: 'Products', href: getProductsUrl(), current: false },
    { name: 'About', href: '/about', current: false },
    { name: 'Contact', href: '/contact', current: false },
  ]

  return (
    <Disclosure as="nav" className="sticky top-0 z-50 bg-gray-50/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-border shadow-xs">
      <div className="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
        <div className="relative flex h-16 items-center justify-between">
          {/* Mobile menu button - Fixed positioning */}
          <div className="flex items-center sm:hidden">
            <DisclosureButton className="group relative inline-flex items-center justify-center rounded-lg p-2 text-foreground hover:bg-muted hover:text-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ring transition-all duration-200">
              <span className="absolute -inset-0.5" />
              <span className="sr-only">Open main menu</span>
              <Bars3Icon
                aria-hidden="true"
                className="block size-6 group-data-[open]:hidden"
              />
              <XMarkIcon
                aria-hidden="true"
                className="hidden size-6 group-data-[open]:block"
              />
            </DisclosureButton>
          </div>

          {/* Logo Section - Centered on mobile, left on desktop */}
          <div className="flex flex-1 justify-center sm:justify-start sm:flex-initial">
            <div className="flex shrink-0 items-center">
              <Link href="/" className="flex items-center">
                <Image
                  alt="Logo of Nutrient Insight"
                  src={settings?.logo ? urlFor(settings.logo).url() : '/logo.png'}
                  width={40}
                  height={40}
                  className="rounded-md"
                />
                <span className="ml-2 text-xl font-bold text-foreground hidden sm:block">
                  Nutrient Insight
                </span>
              </Link>
            </div>
          </div>

          {/* Desktop Navigation - Hidden on tablet and mobile */}
          <div className="hidden lg:flex flex-1 justify-center">
            <div className="flex items-center space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  aria-current={item.current ? 'page' : undefined}
                  className={cn(
                    item.current
                      ? 'border-b-2 border-primary text-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:border-b-2 hover:border-muted',
                    'px-3 py-2 text-sm font-medium transition-all duration-200 border-b-2 border-transparent'
                  )}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Desktop Right Side Actions */}
          <div className="hidden lg:flex items-center space-x-4">
            {/* Article SearchBar */}
            <ArticleSearchBar />

            {/* Wishlist Button */}
            <Link
              href={getWishlistUrl()}
              className="relative p-2 text-muted-foreground hover:bg-muted hover:text-foreground rounded-lg transition-all duration-200 group"
              title="Wishlist"
            >
              <HeartIcon className="size-5 group-hover:scale-110 transition-transform duration-200" />
            </Link>

            {/* Authentication */}
            {session ? (
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  {session.user?.image && (
                    <Image
                      src={session.user.image}
                      alt={session.user.name || 'User'}
                      width={28}
                      height={28}
                      className="rounded-full border border-gray-300 dark:border-gray-600"
                    />
                  )}
                  <span className="text-sm text-muted-foreground hidden xl:block">
                    {session.user?.name?.split(' ')[0] || 'User'}
                  </span>
                </div>
                <button
                  onClick={() => signOut({ callbackUrl: getProductsUrl() })}
                  className="text-muted-foreground hover:bg-muted hover:text-foreground px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <button
                onClick={() => signIn('google', { callbackUrl: getWishlistUrl() })}
                className="flex items-center space-x-2 text-muted-foreground hover:bg-muted hover:text-foreground px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 group"
              >
                <UserIcon className="size-4 group-hover:scale-110 transition-transform duration-200" />
                <span className="hidden xl:block">Sign In</span>
              </button>
            )}

            {/* Theme Switch */}
            <div className="flex items-center">
              <ThemeSwitch />
            </div>
          </div>

          {/* Tablet Navigation (sm to lg) */}
          <div className="hidden sm:flex lg:hidden items-center space-x-3">
            <ArticleSearchBar />
            <Link
              href={getWishlistUrl()}
              className="p-2 text-muted-foreground hover:bg-muted hover:text-foreground rounded-lg transition-all duration-200 group"
              title="Wishlist"
            >
              <HeartIcon className="size-5 group-hover:scale-110 transition-transform duration-200" />
            </Link>

            {/* Simplified auth for tablet */}
            {session ? (
              <div className="flex items-center space-x-2">
                {session.user?.image && (
                  <Image
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                    width={28}
                    height={28}
                    className="rounded-full border border-gray-300 dark:border-gray-600"
                  />
                )}
                <button
                  onClick={() => signOut({ callbackUrl: getProductsUrl() })}
                  className="text-muted-foreground hover:bg-muted hover:text-foreground p-2 rounded-lg text-sm font-medium transition-all duration-200 group"
                  title="Sign Out"
                >
                  <UserIcon className="size-5 group-hover:scale-110 transition-transform duration-200" />
                </button>
              </div>
            ) : (
              <button
                onClick={() => signIn('google', { callbackUrl: getWishlistUrl() })}
                className="p-2 text-muted-foreground hover:bg-muted hover:text-foreground rounded-lg transition-all duration-200 group"
                title="Sign In"
              >
                <UserIcon className="size-5 group-hover:scale-110 transition-transform duration-200" />
              </button>
            )}

            <ThemeSwitch />
          </div>

          {/* Mobile Right Side - Only essential items */}
          <div className="flex items-center space-x-2 sm:hidden">
            <ArticleSearchBar />
            <Link
              href={getWishlistUrl()}
              className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-all duration-200 group"
              title="Wishlist"
            >
              <HeartIcon className="size-5 group-hover:scale-110 transition-transform duration-200" />
            </Link>
          </div>
        </div>
      </div>

      <DisclosurePanel className="sm:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
        <div className="space-y-1 px-2 pb-3 pt-2">
          {/* Main Navigation */}
          {navigation.map((item) => (
            <DisclosureButton
              key={item.name}
              as="a"
              href={item.href}
              aria-current={item.current ? 'page' : undefined}
              className={cn(
                item.current
                  ? 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white border-l-4 border-blue-500'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white',
                'block rounded-md px-3 py-2 text-base font-medium'
              )}
            >
              {item.name}
            </DisclosureButton>
          ))}

          {/* Search Section */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4 pb-4">
            <div className="px-3 mb-2">
              <ArticleSearchBar className="w-full" />
            </div>
          </div>

          {/* Account & Actions Section */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <DisclosureButton
              as="a"
              href={getWishlistUrl()}
              className="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white rounded-md px-3 py-2 text-base font-medium"
            >
              <HeartIcon className="size-5" />
              <span>Wishlist</span>
            </DisclosureButton>

            {session ? (
              <div className="mt-2">
                <div className="flex items-center space-x-3 px-3 py-2 text-gray-700 dark:text-gray-300">
                  {session.user?.image && (
                    <Image
                      src={session.user.image}
                      alt={session.user.name || 'User'}
                      width={32}
                      height={32}
                      className="rounded-full border border-gray-300 dark:border-gray-600"
                    />
                  )}
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {session.user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {session.user?.email}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => signOut({ callbackUrl: getProductsUrl() })}
                  className="w-full text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white rounded-md px-3 py-2 text-base font-medium mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <UserIcon className="size-5" />
                    <span>Sign Out</span>
                  </div>
                </button>
              </div>
            ) : (
              <button
                onClick={() => signIn('google', { callbackUrl: getWishlistUrl() })}
                className="w-full text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white rounded-md px-3 py-2 text-base font-medium mt-2"
              >
                <div className="flex items-center space-x-2">
                  <UserIcon className="size-5" />
                  <span>Sign In</span>
                </div>
              </button>
            )}
          </div>

          {/* Theme Switch */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4 px-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300 text-base font-medium">Theme</span>
              <div className="flex items-center">
                <ThemeSwitch />
              </div>
            </div>
          </div>
        </div>
      </DisclosurePanel>
    </Disclosure>
  )
}
