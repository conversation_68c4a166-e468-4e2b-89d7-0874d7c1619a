"use client";

import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
} from "@heroicons/react/24/outline";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

const ThemeSwitch = () => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <ToggleGroup
      type="single"
      className="flex justify-start gap-2"
      value={theme}
      onValueChange={(value) => value && setTheme(value)}
    >
      <ToggleGroupItem
        value="light"
        aria-label="Light Mode"
        className={`p-2 rounded-md border border-border bg-background text-foreground hover:bg-muted focus:ring-2 focus:ring-ring transition-all duration-200 ${
          theme === "light" ? "bg-primary text-primary-foreground shadow-xs" : "hover:shadow-xs"
        }`}
      >
        <SunIcon className="size-5" />
      </ToggleGroupItem>
      <ToggleGroupItem
        value="system"
        aria-label="System Mode"
        className={`p-2 rounded-md border border-border bg-background text-foreground hover:bg-muted focus:ring-2 focus:ring-ring transition-all duration-200 ${
          theme === "system" ? "bg-primary text-primary-foreground shadow-xs" : "hover:shadow-xs"
        }`}
      >
        <ComputerDesktopIcon className="size-5" />
      </ToggleGroupItem>
      <ToggleGroupItem
        value="dark"
        aria-label="Dark Mode"
        className={`p-2 rounded-md border border-border bg-background text-foreground hover:bg-muted focus:ring-2 focus:ring-ring transition-all duration-200 ${
          theme === "dark" ? "bg-primary text-primary-foreground shadow-xs" : "hover:shadow-xs"
        }`}
      >
        <MoonIcon className="size-5" />
      </ToggleGroupItem>
    </ToggleGroup>
  );
};

export default ThemeSwitch;
