import { cn } from "@/lib/utils";

type ContainerProps = Readonly<{
  large?: boolean;
  alt?: boolean;
  className?: string;
  page?:boolean
  children: React.ReactNode;
}>;

export default function Container({
  large = false,
  alt = false,
  className = "",
  page=false,
  children,
}: ContainerProps) {
  return (
    <div
      className={cn(
        "container px-8 mx-auto xl:px-5",
        large ? "max-w-screen-xl" : "max-w-screen-lg",
        !alt && !page && "py-5 lg:py-8",
        page && "min-h-screen py-8 lg:py-12",
        className
      )}
    >
      {children}
    </div>
  );
}
