'use client'

import Link from 'next/link'
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/20/solid'
import useCountryStore from '@/store/useCountryStore'
import type { Product } from '@/types/Product'

interface BreadcrumbNavigationProps {
  product: Product
  className?: string
}

interface Breadcrumb {
  name: string
  href: string
  icon?: React.ComponentType<{ className?: string; 'aria-hidden'?: boolean }>
  current?: boolean
}

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({ 
  product, 
  className = '' 
}) => {
  const { selectedCountry } = useCountryStore()
  
  // Helper function to build URLs with country parameter
  const buildUrlWithCountry = (basePath: string, additionalParams: string = '') => {
    const params = new URLSearchParams()
    
    if (selectedCountry) {
      params.set('country', selectedCountry)
    }
    
    if (additionalParams) {
      const additionalParamsObj = new URLSearchParams(additionalParams)
      additionalParamsObj.forEach((value, key) => {
        params.set(key, value)
      })
    }
    
    const queryString = params.toString()
    return queryString ? `${basePath}?${queryString}` : basePath
  }
  
  const breadcrumbs: Breadcrumb[] = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Products', href: buildUrlWithCountry('/products') },
  ]

  if (product?.productCategory) {
    breadcrumbs.push({
      name: product.productCategory,
      href: buildUrlWithCountry('/products', `category=${encodeURIComponent(product.productCategory)}`)
    })
  }

  if (product?.name) {
    breadcrumbs.push({
      name: product.name,
      href: buildUrlWithCountry(`/products/${product.slug?.current}`),
      current: true
    })
  }

  return (
    <div className={`flex relative z-10 ${className}`} role="navigation" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.name} className="flex items-center">
            {index > 0 && (
              <ChevronRightIcon
                className="flex-shrink-0 h-4 w-4 text-gray-400 dark:text-gray-500 mx-2"
                aria-hidden={true}
              />
            )}
            {breadcrumb.current ? (
              <span className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate max-w-xs">
                {breadcrumb.name}
              </span>
            ) : (
              <Link
                href={breadcrumb.href}
                className="text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors flex items-center no-underline"
              >
                {breadcrumb.icon && (
                  <breadcrumb.icon className="flex-shrink-0 h-4 w-4 mr-1" aria-hidden={true} />
                )}
                {breadcrumb.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </div>
  )
}

export default BreadcrumbNavigation
