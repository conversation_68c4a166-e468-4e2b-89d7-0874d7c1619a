import CountrySelector from '@/components/products/CountrySelector'
import SearchBar from '@/components/products/SearchBar'

interface PageHeaderProps {
  onCountryChange: (country: string) => void
  searchQuery: string
  onSearchChange: (query: string) => void
  isSearchLoading?: boolean
}

const PageHeader: React.FC<PageHeaderProps> = ({
  onCountryChange,
  searchQuery,
  onSearchChange,
  isSearchLoading = false,
}) => {
  return (
    <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-6 space-y-4 sm:space-y-0">
          {/* Page Title */}
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Products
            </h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Discover amazing products from top retailers
            </p>
          </div>

          {/* Search and Country Selection */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <SearchBar
              searchQuery={searchQuery}
              onSearchChange={onSearchChange}
              isLoading={isSearchLoading}
              className="w-full sm:w-80"
            />

            {/* Search loading indicator - positioned between search and country selector on mobile */}
            <div className={`block sm:hidden transition-all duration-300 ease-in-out overflow-hidden ${
              isSearchLoading
                ? 'max-h-20 opacity-100 transform translate-y-0'
                : 'max-h-0 opacity-0 transform -translate-y-2'
            }`}>
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full size-3 border-b-2 border-blue-500"></div>
                  <span>Searching products...</span>
                </div>
              </div>
            </div>

            <CountrySelector
              onCountryChange={onCountryChange}
              className="w-full sm:w-48"
            />
          </div>

          {/* Search loading indicator for desktop - below both elements */}
          <div className={`hidden sm:block transition-all duration-300 ease-in-out overflow-hidden ${
            isSearchLoading
              ? 'max-h-20 opacity-100 transform translate-y-0 mt-4'
              : 'max-h-0 opacity-0 transform -translate-y-2 mt-0'
          }`}>
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800 w-fit">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full size-3 border-b-2 border-blue-500"></div>
                <span>Searching products...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PageHeader
