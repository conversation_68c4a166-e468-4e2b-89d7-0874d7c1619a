'use client'

import Link from 'next/link'
import useCountryStore from '@/store/useCountryStore'
import ProductCard from '@/components/products/ProductCard'
import type { Product } from '@/types/Product'

interface RelatedProductsProps {
  relatedProducts?: Product[]
  className?: string
}

const RelatedProducts: React.FC<RelatedProductsProps> = ({
  relatedProducts,
  className = ''
}) => {
  const { selectedCountry } = useCountryStore()
  // Filter related products by selected country
  const filteredRelatedProducts = relatedProducts?.filter(product =>
    product.supportedCountries.includes(selectedCountry)
  ) || []

  // Create URL with preserved country
  const getProductsUrl = () => {
    if (selectedCountry) {
      return `/products?country=${encodeURIComponent(selectedCountry)}`
    }
    return '/products'
  }
  if (filteredRelatedProducts.length === 0) {
    return null
  }

  return (
    <div className={`${className}`}>
      <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Related Products
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {filteredRelatedProducts.map((product) => (
            <ProductCard key={product._id} product={product} />
          ))}
        </div>

        {filteredRelatedProducts.length >= 4 && (
          <div className="mt-8 text-center">
            <Link
              href={getProductsUrl()}
              className="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 shadow-xs text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              View All Products
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}

export default RelatedProducts
