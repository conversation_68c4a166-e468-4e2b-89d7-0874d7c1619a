import { useState, useEffect } from 'react'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { useDebounce } from '@/app/hooks/useDebounce'

interface SearchBarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  isLoading?: boolean
  className?: string
}

const SearchBar: React.FC<SearchBarProps> = ({ 
  searchQuery, 
  onSearchChange, 
  isLoading = false, 
  className = '' 
}) => {
  const [localSearch, setLocalSearch] = useState<string>(searchQuery || '')
  const debouncedSearch = useDebounce(localSearch, 300)

  useEffect(() => {
    if (debouncedSearch !== searchQuery) {
      onSearchChange(debouncedSearch)
    }
  }, [debouncedSearch, onSearchChange, searchQuery])

  useEffect(() => {
    setLocalSearch(searchQuery || '')
  }, [searchQuery])

  const handleClear = (): void => {
    setLocalSearch('')
    onSearchChange('')
  }

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon
            className={`size-5 transition-colors ${
              isLoading
                ? 'text-blue-500 animate-pulse'
                : 'text-gray-400 dark:text-gray-500'
            }`}
            aria-hidden="true"
          />
        </div>
        <input
          type="text"
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-gray-500 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          placeholder="Search products..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
        />
        {localSearch && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none"
              onClick={handleClear}
            >
              <XMarkIcon className="size-5" aria-hidden="true" />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default SearchBar
