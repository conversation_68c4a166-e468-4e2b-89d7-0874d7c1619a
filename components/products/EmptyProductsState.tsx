'use client'

import Link from 'next/link'
import useCountryStore from '@/store/useCountryStore'

interface EmptyProductsStateProps {
  isSearch?: boolean
  searchQuery?: string
  category?: string
}

export default function EmptyProductsState({ 
  isSearch = false, 
  searchQuery = '', 
  category = 'All' 
}: EmptyProductsStateProps) {
  const { selectedCountry } = useCountryStore()
  
  // Create URL with preserved country
  const getProductsUrl = () => {
    if (selectedCountry) {
      return `/products?country=${encodeURIComponent(selectedCountry)}`
    }
    return '/products'
  }
  return (
    <div className="text-center py-12">
      <svg
        className="mx-auto size-12 text-gray-400 dark:text-gray-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m0 0V9a2 2 0 012-2h2m0 0V6a2 2 0 012-2h2.586a1 1 0 01.707.293l2.414 2.414A1 1 0 0016 7.414V9"
        />
      </svg>
      
      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
        {isSearch ? 'No products found' : 'No products available'}
      </h3>
      
      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
        {isSearch && searchQuery ? (
          <>No products match &quot;{searchQuery}&quot; in {category === 'All' ? 'any category' : category}.</>
        ) : category !== 'All' ? (
          <>No products found in the {category} category.</>
        ) : (
          <>No products are currently available.</>
        )}
      </p>

      <div className="mt-6 space-y-4">
        {isSearch ? (
          <div className="space-y-2">
            <Link
              href={getProductsUrl()}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
            >
              View All Products
            </Link>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Try searching with different keywords or browse all categories
            </p>
          </div>
        ) : category !== 'All' ? (
          // When filtering by specific category, show simple message with link to all products
          <div className="space-y-2">
            <Link
              href={getProductsUrl()}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
            >
              View All Products
            </Link>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Browse other categories or view all available products
            </p>
          </div>
        ) : (
          // Simple message when no products exist overall
          <div className="space-y-2">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              No products are currently available. Please check back later.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
