'use client'

import { getCurrencySymbol } from '@/lib/affiliateCountries'
import type { Product } from '@/types/Product'
import { usePriceUpdate } from '@/app/hooks/usePriceUpdate'

interface ProductInformationProps {
  product: Product
  className?: string
}

const ProductInformation: React.FC<ProductInformationProps> = ({
  product,
  className = ''
}) => {
  const currencySymbol = getCurrencySymbol(product.currencySymbol)
  const currentPrice = usePriceUpdate(product)

  return (
    <div className={`${className}`}>
      {/* Product Category */}
      {product.productCategory && (
        <div className="mb-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200">
            {product.productCategory}
          </span>
        </div>
      )}

      {/* Product Name */}
      <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        {product.name}
      </h1>

      {/* Price */}
      <div className="mb-6">
        <div className="flex items-baseline space-x-2">
          <span className="text-4xl font-bold text-gray-900 dark:text-gray-100">
            {currencySymbol}
            {currentPrice}
          </span>
          <span className="text-lg text-gray-500 dark:text-gray-400">
            {product.currencySymbol}
          </span>
        </div>
      </div>

      {/* Supported Countries */}
      {product.supportedCountries && product.supportedCountries.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            Available in:
          </h3>
          <div className="flex flex-wrap gap-1">
            {product.supportedCountries.slice(0, 5).map((country) => (
              <span
                key={country}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200"
              >
                {country}
              </span>
            ))}
            {product.supportedCountries.length > 5 && (
              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                +{product.supportedCountries.length - 5} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Product Description */}
      {product.description && (
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
            Description
          </h3>
          <div className="prose prose-sm dark:prose-invert max-w-none">
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              {product.description}
            </p>
          </div>
        </div>
      )}

      {/* Retailer Information */}
      {product.retailer && (
        <div className="mb-6">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Sold by:
            </span>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
              {product.retailer}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProductInformation
