'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { HeartIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'
import useWishlistStore from '@/store/useWishlistStore'
import { getCurrencySymbol } from '@/lib/affiliateCountries'
import type { Product } from '@/types/Product'

interface ProductCardProps {
  product: Product
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [imageLoading, setImageLoading] = useState<boolean>(true)
  const [imageError, setImageError] = useState<boolean>(false)
  
  const { items, toggleItem, isLoading, hasHydrated } = useWishlistStore()
  const isWishlisted = hasHydrated && items.includes(product._id)
  const currencySymbol = getCurrencySymbol(product.currencySymbol)

  const handleWishlistClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!hasHydrated) return
    
    toggleItem(product._id)
  }

  const handleBuyClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault()
    e.stopPropagation()
    if (product.affiliateLink) {
      window.open(product.affiliateLink, '_blank', 'noopener,noreferrer')
    }
  }

  const getRetailerButtonStyle = (retailer?: string): string => {
    switch (retailer?.toLowerCase()) {
      case 'amazon':
        return 'bg-orange-500 hover:bg-orange-600 text-white'
      case 'flipkart':
        return 'bg-blue-500 hover:bg-blue-600 text-white'
      default:
        return 'bg-indigo-600 hover:bg-indigo-700 text-white'
    }
  }

  return (
    <div className="group relative bg-card rounded-lg shadow-xs hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-border">
      <Link href={`/products/${product.slug?.current}`}>
        <div className="cursor-pointer">
          {/* Product Image */}
          <div className="relative aspect-square overflow-hidden rounded-t-lg bg-gray-100 dark:bg-gray-700">
            {product.mainImage?.asset?.url && !imageError ? (
              <Image
                src={product.mainImage.asset.url}
                alt={product.name}
                fill
                className={`object-cover transition-transform duration-300 group-hover:scale-105 ${
                  imageLoading ? 'opacity-0' : 'opacity-100'
                }`}
                onLoad={() => setImageLoading(false)}
                onError={() => setImageError(true)}
                loading="lazy" // Products should lazy load
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500">
                <svg className="size-12" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
            )}

            {imageLoading && !imageError && (
              <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse" />
            )}

            {/* Wishlist Button */}
            <button
              onClick={handleWishlistClick}
              disabled={isLoading || !hasHydrated}
              className="absolute top-3 right-3 p-2 rounded-full bg-white dark:bg-gray-800 shadow-md hover:shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100 disabled:opacity-50"
              aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
            >
              {isWishlisted ? (
                <HeartSolidIcon className="h-5 w-5 text-red-500" />
              ) : (
                <HeartIcon className="h-5 w-5 text-gray-600 dark:text-gray-400 hover:text-red-500" />
              )}
            </button>

            {/* Category Badge */}
            {product.productCategory && (
              <div className="absolute top-3 left-3 px-2 py-1 bg-black/70 text-white text-xs rounded-md">
                {product.productCategory}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 line-clamp-2 mb-2 h-14 leading-7">
              {product.name}
            </h3>

            <div className="flex items-center justify-between mb-3">
              <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {currencySymbol}{product.price}
              </span>
            </div>
          </div>
        </div>
      </Link>

      {/* Buy Button - Outside Link to prevent nesting */}
      <div className="px-4 pb-4">
        <button
          onClick={handleBuyClick}
          className={`w-full py-2 px-4 rounded-md font-medium transition-colors duration-200 ${getRetailerButtonStyle(product.retailer)}`}
          disabled={!product.affiliateLink}
        >
          Buy from {product.retailer || 'Store'}
        </button>
      </div>
    </div>
  )
}

export default ProductCard
