import { useState, useEffect } from 'react'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface CategoryNavigationProps {
  categories?: string[]
  selectedCategory: string
  onCategoryChange: (category: string) => void
  isLoading?: boolean
}

const CategoryNavigation: React.FC<CategoryNavigationProps> = ({
  categories = [],
  selectedCategory,
  onCategoryChange,
  isLoading = false
}) => {
  const [canScrollLeft, setCanScrollLeft] = useState<boolean>(false)
  const [canScrollRight, setCanScrollRight] = useState<boolean>(false)
  const [scrollContainer, setScrollContainer] = useState<HTMLDivElement | null>(null)

  // Categories already include 'All' from the server, so don't add it again
  const allCategories = categories

  useEffect(() => {
    if (scrollContainer) {
      const checkScrollability = (): void => {
        setCanScrollLeft(scrollContainer.scrollLeft > 0)
        setCanScrollRight(
          scrollContainer.scrollLeft <
          scrollContainer.scrollWidth - scrollContainer.clientWidth
        )
      }

      checkScrollability()
      scrollContainer.addEventListener('scroll', checkScrollability)

      return () => {
        scrollContainer.removeEventListener('scroll', checkScrollability)
      }
    }
  }, [scrollContainer, categories])

  const scroll = (direction: 'left' | 'right'): void => {
    if (scrollContainer) {
      const scrollAmount = 200
      scrollContainer.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      })
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-4">
            <div className="flex space-x-4">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative py-4">
          {/* Left scroll button */}
          {canScrollLeft && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white dark:bg-gray-900 shadow-md rounded-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <ChevronLeftIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
          )}

          {/* Categories container */}
          <div
            ref={setScrollContainer}
            className="flex space-x-3 overflow-x-auto scrollbar-hide scroll-smooth py-2 px-2"
            style={{ 
              scrollbarWidth: 'none', 
              msOverflowStyle: 'none',
              paddingLeft: canScrollLeft ? '3rem' : '1rem',
              paddingRight: canScrollRight ? '3rem' : '1rem'
            }}
          >
            {allCategories.map((category) => (
              <button
                key={category}
                onClick={() => onCategoryChange(category)}
                className={`flex-shrink-0 px-5 py-3 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap min-w-fit focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-900 ${
                  selectedCategory === category
                    ? 'bg-indigo-600 dark:bg-indigo-500 text-white shadow-lg ring-2 ring-indigo-500 ring-offset-2 dark:ring-offset-gray-900'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 hover:shadow-md border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Right scroll button */}
          {canScrollRight && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white dark:bg-gray-900 shadow-md rounded-full p-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <ChevronRightIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default CategoryNavigation
