import { useState, useEffect, useRef, useCallback } from 'react'
import ProductCard from '@/components/products/ProductCard'
import EmptyProductsState from '@/components/products/EmptyProductsState'
import type { Product } from '@/types/Product'

interface ProductGridProps {
  products: Product[]
  isLoading: boolean
  hasMore: boolean
  onLoadMore: () => Promise<void>
  isEmpty?: boolean
  searchQuery?: string
  category?: string
}

const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  isLoading,
  hasMore,
  onLoadMore,
  isEmpty = false,
  searchQuery = '',
  category = 'All',
}) => {
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadMoreRef = useRef<HTMLDivElement | null>(null)

  const handleLoadMore = useCallback(async (): Promise<void> => {
    if (isLoadingMore || !hasMore) return

    setIsLoadingMore(true)
    try {
      await onLoadMore()
    } finally {
      setIsLoadingMore(false)
    }
  }, [isLoadingMore, hasMore, onLoadMore])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          hasMore &&
          !isLoading &&
          !isLoadingMore
        ) {
          handleLoadMore()
        }
      },
      { threshold: 0.1 }
    )

    const currentLoadMoreRef = loadMoreRef.current
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef)
    }

    observerRef.current = observer

    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef)
      }
      observer.disconnect()
    }
  }, [hasMore, isLoading, isLoadingMore, handleLoadMore])

  // Loading skeleton with proper container
  const LoadingSkeleton: React.FC = () => (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
          >
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-t-lg animate-pulse" />
            <div className="p-4 space-y-3">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse" />
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  if (isLoading && products.length === 0) {
    return <LoadingSkeleton />
  }

  if (isEmpty && products.length === 0) {
    return (
      <EmptyProductsState 
        isSearch={searchQuery !== ''}
        searchQuery={searchQuery}
        category={category}
      />
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard key={product._id} product={product} />
        ))}
      </div>

      {/* Load More Trigger */}
      {hasMore && (
        <div ref={loadMoreRef} className="mt-8 text-center">
          {isLoadingMore && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700"
                >
                  <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-t-lg animate-pulse" />
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse" />
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* End of results message */}
      {!hasMore && products.length > 0 && (
        <div className="mt-8 text-center text-sm text-gray-500 dark:text-gray-400">
          You&apos;ve reached the end of the results
        </div>
      )}
    </div>
  )
}

export default ProductGrid
