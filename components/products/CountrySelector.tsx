import { Fragment, useState, useEffect } from 'react'
import { Listbox, Transition } from '@headlessui/react'
import { ChevronUpDownIcon, CheckIcon } from '@heroicons/react/20/solid'
import { SUPPORTED_COUNTRIES } from '@/lib/affiliateCountries'
import useCountryStore from '@/store/useCountryStore'

interface CountrySelectorProps {
  onCountryChange: (country: string) => void
  className?: string
}

const CountrySelector: React.FC<CountrySelectorProps> = ({
  onCountryChange,
  className = '',
}) => {
  const [mounted, setMounted] = useState<boolean>(false)
  const { selectedCountry, setSelectedCountry, initializeCountry } = useCountryStore()

  useEffect(() => {
    setMounted(true)
    // Initialize the store if it hasn't been initialized yet
    initializeCountry()
  }, [initializeCountry])

  const handleCountryChange = (country: string): void => {
    setSelectedCountry(country)
    onCountryChange(country)
  }

  if (!mounted) {
    return (
      <div className="w-48 h-10 bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse" />
    )
  }

  return (
    <div className={`relative ${className}`}>
      <Listbox value={selectedCountry} onChange={handleCountryChange}>
        <div className="relative">
          <Listbox.Button className="relative w-full cursor-default rounded-lg bg-white dark:bg-gray-800 py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm border border-gray-300 dark:border-gray-600">
            <span className="block truncate text-gray-900 dark:text-gray-100">
              {selectedCountry || 'Select Country'}
            </span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronUpDownIcon
                className="size-5 text-gray-400"
                aria-hidden="true"
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              {SUPPORTED_COUNTRIES.map((country) => (
                <Listbox.Option
                  key={country.code}
                  className={({ active }) =>
                    `relative cursor-default select-none py-2 pl-10 pr-4 ${
                      active
                        ? 'bg-amber-100 dark:bg-amber-900 text-amber-900 dark:text-amber-100'
                        : 'text-gray-900 dark:text-gray-100'
                    }`
                  }
                  value={country.name}
                >
                  {({ selected }) => (
                    <>
                      <span
                        className={`block truncate ${
                          selected ? 'font-medium' : 'font-normal'
                        }`}
                      >
                        {country.name}
                      </span>
                      {selected ? (
                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600 dark:text-amber-400">
                          <CheckIcon className="size-5" aria-hidden="true" />
                        </span>
                      ) : null}
                    </>
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
    </div>
  )
}

export default CountrySelector
