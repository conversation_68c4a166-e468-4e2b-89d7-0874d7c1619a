'use client'

import { useState } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

const DevelopmentBanner = () => {
  const [isVisible, setIsVisible] = useState(true)
  
  // Only show in development mode
  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return null
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-3">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="size-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Development Mode:</strong> You&apos;re viewing mock product data.
                Add real products to your Sanity CMS or set <code className="bg-yellow-100 dark:bg-yellow-800 px-1 rounded">USE_MOCK_PRODUCTS=false</code> in your environment.
              </p>
            </div>
          </div>
          <div className="flex-shrink-0">
            <button
              type="button"
              className="rounded-md bg-yellow-50 dark:bg-yellow-900/20 p-1.5 text-yellow-500 dark:text-yellow-400 hover:bg-yellow-100 dark:hover:bg-yellow-800/30 transition-colors"
              onClick={() => setIsVisible(false)}
            >
              <span className="sr-only">Dismiss</span>
              <XMarkIcon className="size-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DevelopmentBanner

