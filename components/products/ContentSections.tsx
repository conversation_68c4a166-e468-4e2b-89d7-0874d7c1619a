'use client'

import { useState } from 'react'
import { Tab } from '@headlessui/react'
import { PortableText } from '@portabletext/react'
import type { Product } from '@/types/Product'

interface ContentSectionsProps {
  product: Product
  className?: string
}

interface ContentTab {
  name: string
  content: React.ReactNode
}

const ContentSections: React.FC<ContentSectionsProps> = ({ 
  product, 
  className = '' 
}) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(0)

  // Prepare tabs data
  const tabs: ContentTab[] = []

  // Description tab (always present if description exists)
  if (product.description) {
    tabs.push({
      name: 'Description',
      content: (
        <div className="prose prose-sm dark:prose-invert max-w-none">
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
            {product.description}
          </p>
        </div>
      )
    })
  }

  // Specifications tab (if specifications exist)
  if (product.specifications && product.specifications.length > 0) {
    tabs.push({
      name: 'Specifications',
      content: (
        <div className="prose prose-sm dark:prose-invert max-w-none">
          {/* Check if specifications are key-value pairs or PortableText */}
          {Array.isArray(product.specifications) && 
           product.specifications.length > 0 && 
           typeof product.specifications[0] === 'object' && 
           'key' in product.specifications[0] && 
           'value' in product.specifications[0] ? (
            // Render as key-value pairs (from Amazon scraper)
            <div className="space-y-3">
              {(product.specifications as Array<{key: string, value: string}>).map((spec, index: number) => (
                <div key={index} className="flex flex-col sm:flex-row sm:items-start border-b border-gray-200 dark:border-gray-700 pb-3">
                  <div className="sm:w-1/3">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {spec.key}:
                    </span>
                  </div>
                  <div className="sm:w-2/3">
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {spec.value}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Render as PortableText (for rich text specifications)
            <PortableText
              value={product.specifications}
              components={{
                block: {
                  normal: ({ children }) => (
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                      {children}
                    </p>
                  ),
                  h1: ({ children }) => (
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }) => (
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      {children}
                    </h3>
                  ),
                },
                list: {
                  bullet: ({ children }) => (
                    <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300 mb-4">
                      {children}
                    </ul>
                  ),
                  number: ({ children }) => (
                    <ol className="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300 mb-4">
                      {children}
                    </ol>
                  ),
                },
                listItem: {
                  bullet: ({ children }) => <li>{children}</li>,
                  number: ({ children }) => <li>{children}</li>,
                },
                marks: {
                  strong: ({ children }) => (
                    <strong className="font-semibold text-gray-900 dark:text-gray-100">
                      {children}
                    </strong>
                  ),
                  em: ({ children }) => (
                    <em className="italic">{children}</em>
                  ),
                },
              }}
            />
          )}
        </div>
      )
    })
  }

  // Additional Information tab (placeholder for future use)
  // Note: Currently not implemented in Product schema
  // if (product.additionalInfo) {
  //   tabs.push({
  //     name: 'Additional Info',
  //     content: (
  //       <div className="prose prose-sm dark:prose-invert max-w-none">
  //         <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
  //           {product.additionalInfo}
  //         </p>
  //       </div>
  //     )
  //   })
  // }

  // If no tabs, don't render anything
  if (tabs.length === 0) {
    return null
  }

  // If only one tab, render without tab interface
  if (tabs.length === 1) {
    return (
      <div className={`${className}`}>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          {tabs[0].name}
        </h3>
        {tabs[0].content}
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      <Tab.Group selectedIndex={selectedIndex} onChange={setSelectedIndex}>
        <Tab.List className="flex space-x-1 rounded-xl bg-gray-100 dark:bg-gray-800 p-1">
          {tabs.map((tab) => (
            <Tab
              key={tab.name}
              className={({ selected }) =>
                `w-full rounded-lg py-2.5 text-sm font-medium leading-5 transition-all ${
                  selected
                    ? 'bg-white dark:bg-gray-700 text-indigo-700 dark:text-indigo-300 shadow'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-gray-100'
                }`
              }
            >
              {tab.name}
            </Tab>
          ))}
        </Tab.List>
        <Tab.Panels className="mt-6">
          {tabs.map((tab, idx) => (
            <Tab.Panel
              key={idx}
              className="rounded-xl bg-white dark:bg-gray-800 p-6 border border-gray-200 dark:border-gray-700"
            >
              {tab.content}
            </Tab.Panel>
          ))}
        </Tab.Panels>
      </Tab.Group>
    </div>
  )
}

export default ContentSections
