'use client'
import { useState } from 'react'
import {
  HeartIcon,
  ShareIcon,
  ArrowTopRightOnSquareIcon,
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'
import useWishlistStore from '@/store/useWishlistStore'
import NativeSocialShare from '@/components/NativeSocialShare'
import { useIsomorphicWindow } from '@/app/hooks/useHydration'
import type { Product } from '@/types/Product'

interface ActionButtonsProps {
  product: Product
  initialIsWishlisted: boolean
  className?: string
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  product,
  initialIsWishlisted: _initialIsWishlisted, // eslint-disable-line @typescript-eslint/no-unused-vars
  className = '',
}) => {
  const [showShareMenu, setShowShareMenu] = useState(false)

  const { items, toggleItem, isLoading, hasHydrated } = useWishlistStore()
  const window = useIsomorphicWindow()
  const isWishlisted = hasHydrated && items.includes(product._id)

  const productUrl = window?.location.href || ''
  const shareTitle = `Check out ${product.name}`
  const shareDescription =
    product.description || `Amazing product from ${product.retailer}`

  const handleWishlistClick = () => {
    if (!hasHydrated) return

    toggleItem(product._id)
  }

  const handleBuyClick = () => {
    if (product.affiliateLink && window) {
      window.open(product.affiliateLink, '_blank', 'noopener,noreferrer')
    }
  }


  const getRetailerButtonStyle = (retailer: string) => {
    switch (retailer?.toLowerCase()) {
      case 'amazon':
        return 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500'
      case 'flipkart':
        return 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-500'
      default:
        return 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
    }
  }

  return (
    <div className={`${className}`}>
      {/* Main Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        {/* Buy Button */}
        <button
          onClick={handleBuyClick}
          disabled={!product.affiliateLink}
          className={`flex-1 flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${getRetailerButtonStyle(product.retailer)}`}
        >
          <ArrowTopRightOnSquareIcon className="size-5 mr-2" />
          Buy from {product.retailer || 'Store'}
        </button>

        {/* Wishlist Button */}
        <button
          onClick={handleWishlistClick}
          disabled={isLoading || !hasHydrated}
          className={`px-6 py-3 border-2 rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
            isWishlisted
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 focus:ring-red-500'
              : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500'
          }`}
        >
          {isWishlisted ? (
            <HeartSolidIcon className="size-5 mx-auto" />
          ) : (
            <HeartIcon className="size-5 mx-auto" />
          )}
        </button>

        {/* Share Button */}
        <div className="relative">
          <button
            onClick={() => setShowShareMenu(!showShareMenu)}
            className="px-6 py-3 border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            <ShareIcon className="size-5 mx-auto" />
          </button>

          {/* Share Menu */}
          {showShareMenu && (
            <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
              <div className="p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Share this product
                </h4>

                {/* Native Social Share */}
                <NativeSocialShare
                  url={productUrl}
                  title={shareTitle}
                  description={shareDescription}
                  hashtags={['Products', 'NutrientInsight']}
                  size={36}
                  className="justify-center mb-3"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close share menu */}
      {showShareMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowShareMenu(false)}
        />
      )}

      {/* Additional Info */}
      <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
        <p>• Free shipping may be available</p>
        <p>• Prices and availability subject to change</p>
        <p>• External links open in new tab</p>
      </div>
    </div>
  )
}

export default ActionButtons
