import CategoryNavigation from '@/components/products/CategoryNavigation'
import SortingOptions from '@/components/products/SortingOptions'

interface NavigationAndFilteringProps {
  categories: string[]
  selectedCategory: string
  onCategoryChange: (category: string) => void
  selectedSort: string
  onSortChange: (sort: string) => void
  isLoadingCategories?: boolean
  productCount?: number
}

const NavigationAndFiltering: React.FC<NavigationAndFilteringProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  selectedSort,
  onSortChange,
  isLoadingCategories = false,
  productCount = 0,
}) => {
  return (
    <div className="bg-white dark:bg-gray-900">
      {/* Category Navigation */}
      <CategoryNavigation
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={onCategoryChange}
        isLoading={isLoadingCategories}
      />

      {/* Sorting and Results Count */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 space-y-3 sm:space-y-0">
            {/* Results count */}
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {productCount > 0 ? (
                <>
                  Showing{' '}
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {productCount}
                  </span>{' '}
                  products
                  {selectedCategory !== 'All' && (
                    <>
                      {' '}
                      in{' '}
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {selectedCategory}
                      </span>
                    </>
                  )}
                </>
              ) : (
                'No products found'
              )}
            </div>

            {/* Sorting options */}
            <SortingOptions
              selectedSort={selectedSort}
              onSortChange={onSortChange}
              className="w-full sm:w-64"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default NavigationAndFiltering
