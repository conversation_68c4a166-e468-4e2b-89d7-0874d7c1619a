'use client'

import React, { useState, useCallback, useTransition } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { getProducts } from '@/app/actions/products'
import PageHeader from '@/components/products/PageHeader'
import NavigationAndFiltering from '@/components/products/NavigationAndFiltering'
import ProductGrid from '@/components/products/ProductGrid'
import useCountryStore from '@/store/useCountryStore'
import type { Product } from '@/types/Product'

interface ProductListingClientProps {
  initialProducts: Product[]
  initialCategories: string[]
  initialHasMore: boolean
  initialParams: {
    country: string
    category: string
    search: string
    sort: string
  }
  shouldRedirectForDefaultCountry?: boolean
}

const PRODUCTS_PER_PAGE = 20

export default function ProductListingClient({
  initialProducts,
  initialCategories,
  initialHasMore,
  initialParams,
  shouldRedirectForDefaultCountry = false,
}: ProductListingClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isPending, startTransition] = useTransition()
  const { selectedCountry, setSelectedCountry, initializeCountry } =
    useCountryStore()

  const [products, setProducts] = useState(initialProducts)
  const [categories] = useState(initialCategories)
  const [hasMore, setHasMore] = useState(initialHasMore)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(initialProducts.length)

  // Update products and total count when initialProducts change
  React.useEffect(() => {
    setProducts(initialProducts)
    setTotalCount(initialProducts.length)
    setHasMore(initialHasMore)
  }, [initialProducts, initialHasMore])

  // Current parameters from URL
  const country = searchParams.get('country') || initialParams.country
  const category = searchParams.get('category') || initialParams.category
  const search = searchParams.get('search') || initialParams.search
  const sort = searchParams.get('sort') || initialParams.sort

  // Update URL parameters
  const updateURL = useCallback(
    (newParams: Record<string, string>) => {
      const params = new URLSearchParams(searchParams.toString())

      Object.entries(newParams).forEach(([key, value]) => {
        // Always include country in URL, even if it's the default
        if (key === 'country') {
          if (value && value !== '') {
            params.set(key, value)
          }
        } else if (value && value !== 'All' && value !== '') {
          params.set(key, value)
        } else {
          params.delete(key)
        }
      })

      // Reset page when filters change
      if (Object.keys(newParams).some((key) => key !== 'page')) {
        params.delete('page')
        setCurrentPage(1)
      }

      startTransition(() => {
        router.push(`/products?${params.toString()}`)
      })
    },
    [router, searchParams]
  )

  // Sync Zustand store with URL country parameter on initial load
  React.useEffect(() => {
    const urlCountry = searchParams.get('country')
    if (urlCountry && urlCountry !== selectedCountry) {
      setSelectedCountry(urlCountry)
    } else if (!urlCountry && selectedCountry) {
      // If no country in URL but we have one in store, update URL
      updateURL({ country: selectedCountry })
    } else if (!urlCountry && !selectedCountry) {
      // Initialize both store and URL with default country
      initializeCountry(initialParams.country)
      updateURL({ country: initialParams.country })
    }
  }, [
    searchParams,
    selectedCountry,
    setSelectedCountry,
    initializeCountry,
    initialParams.country,
    updateURL,
  ])

  React.useEffect(() => {
    if (shouldRedirectForDefaultCountry) {
      updateURL({ country: initialParams.country })
    }
  }, [shouldRedirectForDefaultCountry, initialParams.country, updateURL])

  // Handle parameter changes
  const handleCountryChange = useCallback(
    async (newCountry: string) => {
      try {
        const [sortField, sortOrder] = sort.split('-')
        const result = await getProducts({
          country: newCountry,
          category,
          search,
          sortField,
          sortOrder,
          offset: 0,
          limit: PRODUCTS_PER_PAGE,
        })
        setProducts(result.products)
        setHasMore(result.hasMore)
        setTotalCount(result.total)
        setCurrentPage(1)

        // Update URL after successful fetch
        updateURL({ country: newCountry })
      } catch (error) {
        console.error('Error fetching filtered products by country:', error)
      }
    },
    [updateURL, category, search, sort]
  )

  const handleCategoryChange = useCallback(
    async (newCategory: string) => {
      try {
        const [sortField, sortOrder] = sort.split('-')
        const result = await getProducts({
          country,
          category: newCategory,
          search,
          sortField,
          sortOrder,
          offset: 0,
          limit: PRODUCTS_PER_PAGE,
        })
        setProducts(result.products)
        setHasMore(result.hasMore)
        setTotalCount(result.total)
        setCurrentPage(1)

        // Update URL after successful fetch
        updateURL({ category: newCategory })
      } catch (error) {
        console.error('Error fetching filtered products:', error)
      }
    },
    [updateURL, country, search, sort]
  )

  const handleSearchChange = useCallback(
    (newSearch: string) => {
      updateURL({ search: newSearch })
    },
    [updateURL]
  )

  const handleSortChange = useCallback(
    (newSort: string) => {
      updateURL({ sort: newSort })
    },
    [updateURL]
  )

  // Handle load more with Server Action
  const handleLoadMore = useCallback(async () => {
    if (isLoadingMore || !hasMore) return

    setIsLoadingMore(true)
    try {
      const [sortField, sortOrder] = sort.split('-')
      const nextPage = currentPage + 1
      const offset = (nextPage - 1) * PRODUCTS_PER_PAGE

      const result = await getProducts({
        country,
        category,
        search,
        sortField,
        sortOrder,
        offset,
        limit: PRODUCTS_PER_PAGE,
      })

      setProducts((prev) => [...prev, ...result.products])
      setHasMore(result.hasMore)
      setCurrentPage(nextPage)
    } catch (error) {
      console.error('Error loading more products:', error)
    } finally {
      setIsLoadingMore(false)
    }
  }, [country, category, search, sort, currentPage, isLoadingMore, hasMore])

  return (
    <>
      {/* Page Header */}
      <PageHeader
        onCountryChange={handleCountryChange}
        searchQuery={search}
        onSearchChange={handleSearchChange}
        isSearchLoading={isPending}
      />

      {/* Navigation and Filtering */}
      <NavigationAndFiltering
        categories={categories}
        selectedCategory={category}
        onCategoryChange={handleCategoryChange}
        selectedSort={sort}
        onSortChange={handleSortChange}
        isLoadingCategories={false}
        productCount={totalCount}
      />

      {/* Product Grid */}
      <ProductGrid
        products={products}
        isLoading={isPending && currentPage === 1}
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        isEmpty={!isPending && products.length === 0}
        searchQuery={search}
        category={category}
      />
    </>
  )
}
